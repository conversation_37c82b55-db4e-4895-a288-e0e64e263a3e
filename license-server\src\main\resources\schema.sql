-- License Management System Database Schema
-- 许可证管理系统数据库结构

-- 创建数据库（MySQL）
-- CREATE DATABASE IF NOT EXISTS license_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE license_db;

-- 许可证表
CREATE TABLE IF NOT EXISTS license (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）',
    customer_id VARCHAR(64) NOT NULL COMMENT '客户ID',
    customer_name VARCHAR(200) NOT NULL COMMENT '客户名称',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    product_version VARCHAR(50) COMMENT '产品版本',
    license_type VARCHAR(50) NOT NULL COMMENT '许可证类型',
    machine_id VARCHAR(200) COMMENT '机器标识',
    status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态',
    activation_code VARCHAR(100) UNIQUE COMMENT '激活码',
    issue_time DATETIME NOT NULL COMMENT '签发时间',
    effective_time DATETIME NOT NULL COMMENT '生效时间',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    max_users INT COMMENT '最大用户数',
    max_concurrent INT COMMENT '最大并发数',
    modules TEXT COMMENT '功能模块列表（JSON格式）',
    extensions TEXT COMMENT '扩展属性（JSON格式）',
    signature TEXT COMMENT '数字签名',
    last_verify_time DATETIME COMMENT '最后验证时间',
    verify_count BIGINT DEFAULT 0 COMMENT '验证次数',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remarks VARCHAR(500) COMMENT '备注信息',
    
    INDEX idx_license_id (license_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_machine_id (machine_id),
    INDEX idx_activation_code (activation_code),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time),
    INDEX idx_create_time (create_time),
    INDEX idx_last_verify_time (last_verify_time)
) COMMENT='许可证表';

-- 客户表
CREATE TABLE IF NOT EXISTS customer (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    customer_id VARCHAR(64) NOT NULL UNIQUE COMMENT '客户ID',
    customer_name VARCHAR(200) NOT NULL COMMENT '客户名称',
    company_name VARCHAR(200) COMMENT '公司名称',
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(50) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '地址',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '客户状态',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remarks VARCHAR(500) COMMENT '备注信息',
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_customer_name (customer_name),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) COMMENT='客户表';

-- 产品表
CREATE TABLE IF NOT EXISTS product (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    product_id VARCHAR(64) NOT NULL UNIQUE COMMENT '产品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    product_version VARCHAR(50) COMMENT '产品版本',
    description TEXT COMMENT '产品描述',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '产品状态',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_product_id (product_id),
    INDEX idx_product_name (product_name),
    INDEX idx_status (status)
) COMMENT='产品表';

-- 许可证验证日志表
CREATE TABLE IF NOT EXISTS license_verify_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    license_id VARCHAR(64) NOT NULL COMMENT '许可证ID',
    machine_id VARCHAR(200) COMMENT '机器标识',
    verify_type VARCHAR(20) NOT NULL COMMENT '验证类型（LOCAL/ONLINE）',
    verify_result VARCHAR(20) NOT NULL COMMENT '验证结果（SUCCESS/FAILED）',
    error_code VARCHAR(20) COMMENT '错误码',
    error_message VARCHAR(500) COMMENT '错误信息',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    verify_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '验证时间',
    
    INDEX idx_license_id (license_id),
    INDEX idx_machine_id (machine_id),
    INDEX idx_verify_type (verify_type),
    INDEX idx_verify_result (verify_result),
    INDEX idx_verify_time (verify_time)
) COMMENT='许可证验证日志表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL DEFAULT 'STRING' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key)
) COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    operator VARCHAR(100) COMMENT '操作人',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(200) COMMENT '操作描述',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id VARCHAR(100) COMMENT '目标ID',
    request_data TEXT COMMENT '请求数据',
    response_data TEXT COMMENT '响应数据',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    INDEX idx_operator (operator),
    INDEX idx_operation_type (operation_type),
    INDEX idx_target_type (target_type),
    INDEX idx_target_id (target_id),
    INDEX idx_operation_time (operation_time)
) COMMENT='操作日志表';

-- 插入初始数据
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('license.default.validity.days', '365', 'INTEGER', '默认许可证有效期（天）'),
('license.trial.validity.days', '30', 'INTEGER', '试用版许可证有效期（天）'),
('license.max.users.default', '100', 'INTEGER', '默认最大用户数'),
('license.max.concurrent.default', '50', 'INTEGER', '默认最大并发数'),
('system.version', '1.0.0', 'STRING', '系统版本'),
('system.name', 'License Management System', 'STRING', '系统名称')
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value);

-- 插入示例客户数据
INSERT INTO customer (customer_id, customer_name, company_name, contact_person, contact_email, status) VALUES
('CUST-001', '示例客户A', '示例公司A', '张三', '<EMAIL>', 'ACTIVE'),
('CUST-002', '示例客户B', '示例公司B', '李四', '<EMAIL>', 'ACTIVE'),
('CUST-003', '示例客户C', '示例公司C', '王五', '<EMAIL>', 'ACTIVE')
ON DUPLICATE KEY UPDATE customer_name = VALUES(customer_name);

-- 插入示例产品数据
INSERT INTO product (product_id, product_name, product_version, description, status) VALUES
('PROD-001', '企业管理系统', '1.0.0', '综合性企业管理解决方案', 'ACTIVE'),
('PROD-002', '财务管理系统', '2.0.0', '专业财务管理软件', 'ACTIVE'),
('PROD-003', '人力资源系统', '1.5.0', '人力资源管理平台', 'ACTIVE')
ON DUPLICATE KEY UPDATE product_name = VALUES(product_name);
