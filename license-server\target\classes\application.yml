# License Server Configuration
server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain

spring:
  application:
    name: license-server
  
  # 数据库配置
  datasource:
    # H2 内存数据库（开发环境）
    url: jdbc:h2:mem:license_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
    # MySQL 数据库配置（生产环境）
    # url: ***********************************************************************************************************************
    # driver-class-name: com.mysql.cj.jdbc.Driver
    # username: root
    # password: your_password
    
    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境使用update，生产环境使用validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect  # MySQL: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # H2 控制台配置（仅开发环境）
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: false
  
  # Jackson 配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# 许可证系统配置
license:
  # 密钥管理配置
  key:
    # 私钥文件路径（为空时使用默认路径：用户目录/.license/private.key）
    private-key-path: 
    # 公钥文件路径（为空时使用默认路径：用户目录/.license/public.key）
    public-key-path: 
    # RSA密钥长度
    key-size: 2048
    # 是否自动生成密钥对（当密钥文件不存在时）
    auto-generate: true
  
  # 许可证配置
  license:
    # 默认许可证有效期（天）
    default-validity-days: 365
    # 试用版许可证有效期（天）
    trial-validity-days: 30
    # 许可证文件名
    license-filename: license.dat
    # 公钥文件名
    public-key-filename: public.key
  
  # 验证配置
  validation:
    # 是否启用在线验证
    online-validation-enabled: true
    # 验证缓存时间（秒）
    cache-duration: 3600
    # 最大验证失败次数
    max-validation-failures: 5
    # 验证失败锁定时间（秒）
    lockout-duration: 1800

# 日志配置
logging:
  level:
    root: INFO
    com.robin.license: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/license-server.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 应用信息
info:
  app:
    name: License Management Server
    description: 分布式软件授权管理系统服务端
    version: 1.0.0
    author: Robin
  build:
    java-version: ${java.version}
    spring-boot-version: ${spring-boot.version}

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  # 生产环境数据库配置
  datasource:
    url: **********************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:license_user}
    password: ${DB_PASSWORD:your_secure_password}
    
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境使用validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
  
  # 禁用H2控制台
  h2:
    console:
      enabled: false

# 生产环境日志配置
logging:
  level:
    root: WARN
    com.robin.license: INFO
    org.hibernate.SQL: WARN

# 生产环境许可证配置
license:
  key:
    private-key-path: ${LICENSE_PRIVATE_KEY_PATH:/opt/license/keys/private.key}
    public-key-path: ${LICENSE_PUBLIC_KEY_PATH:/opt/license/keys/public.key}
    auto-generate: false  # 生产环境不自动生成密钥

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.robin.license: DEBUG
    org.hibernate.SQL: DEBUG
