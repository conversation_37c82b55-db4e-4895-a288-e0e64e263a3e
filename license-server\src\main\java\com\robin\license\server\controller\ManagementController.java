package com.robin.license.server.controller;

import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.dto.ApiResponse;
import com.robin.license.server.service.KeyManagementService;
import com.robin.license.server.service.LicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理REST API控制器
 * 提供系统管理和监控功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/management")
@Slf4j
public class ManagementController {
    
    @Autowired
    private LicenseService licenseService;
    
    @Autowired
    private KeyManagementService keyManagementService;
    
    /**
     * 获取系统状态
     * 
     * @return 系统状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("service", "License Management Server");
            status.put("version", "1.0.0");
            status.put("status", "running");
            status.put("timestamp", System.currentTimeMillis());
            
            // 检查密钥状态
            boolean keyPairValid = keyManagementService.validateKeyPair();
            status.put("keyPairValid", keyPairValid);
            
            return ResponseEntity.ok(ApiResponse.success(status));
            
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "获取系统状态失败"));
        }
    }
    
    /**
     * 获取公钥
     * 
     * @return 公钥PEM格式
     */
    @GetMapping("/public-key")
    public ResponseEntity<ApiResponse<String>> getPublicKey() {
        try {
            String publicKeyPem = keyManagementService.getPublicKeyPem();
            return ResponseEntity.ok(ApiResponse.success("获取公钥成功", publicKeyPem));
            
        } catch (Exception e) {
            log.error("获取公钥失败", e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "获取公钥失败"));
        }
    }
    
    /**
     * 重新生成密钥对
     *
     * @return 操作结果
     */
    @PostMapping("/regenerate-keys")
    public ResponseEntity<ApiResponse<String>> regenerateKeys() {
        try {
            log.info("开始重新生成密钥对");
            
            boolean success = keyManagementService.regenerateKeyPair();
            
            if (success) {
                log.info("密钥对重新生成成功");
                return ResponseEntity.ok(ApiResponse.success("密钥对重新生成成功"));
            } else {
                log.error("密钥对重新生成失败");
                return ResponseEntity.ok(ApiResponse.error(
                        LicenseConstants.ErrorCode.SERVER_ERROR, "密钥对重新生成失败"));
            }
            
        } catch (Exception e) {
            log.error("重新生成密钥对异常", e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "重新生成密钥对异常"));
        }
    }
    
    /**
     * 验证密钥对
     * 
     * @return 验证结果
     */
    @PostMapping("/validate-keys")
    public ResponseEntity<ApiResponse<Boolean>> validateKeys() {
        try {
            boolean isValid = keyManagementService.validateKeyPair();
            
            if (isValid) {
                return ResponseEntity.ok(ApiResponse.success("密钥对验证通过", true));
            } else {
                return ResponseEntity.ok(ApiResponse.error(
                        LicenseConstants.ErrorCode.SIGNATURE_VERIFY_FAILED, "密钥对验证失败", false));
            }
            
        } catch (Exception e) {
            log.error("验证密钥对异常", e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "验证密钥对异常", false));
        }
    }
    
    /**
     * 获取许可证统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // TODO: 实现统计功能
            statistics.put("totalLicenses", 0);
            statistics.put("validLicenses", 0);
            statistics.put("expiredLicenses", 0);
            statistics.put("trialLicenses", 0);
            statistics.put("standardLicenses", 0);
            statistics.put("professionalLicenses", 0);
            statistics.put("enterpriseLicenses", 0);
            
            return ResponseEntity.ok(ApiResponse.success(statistics));
            
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "获取统计信息失败"));
        }
    }
    
    /**
     * 健康检查
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, String>>> health() {
        Map<String, String> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return ResponseEntity.ok(ApiResponse.success(health));
    }
}
