package com.robin.license.common.exception;

/**
 * 许可证异常类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class LicenseException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final int errorCode;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public LicenseException(String message) {
        super(message);
        this.errorCode = -1;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     */
    public LicenseException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public LicenseException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = -1;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public LicenseException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public int getErrorCode() {
        return errorCode;
    }
    
    /**
     * 许可证不存在异常
     */
    public static class LicenseNotFoundException extends LicenseException {
        public LicenseNotFoundException(String message) {
            super(1001, message);
        }
    }
    
    /**
     * 许可证无效异常
     */
    public static class LicenseInvalidException extends LicenseException {
        public LicenseInvalidException(String message) {
            super(1002, message);
        }
    }
    
    /**
     * 许可证过期异常
     */
    public static class LicenseExpiredException extends LicenseException {
        public LicenseExpiredException(String message) {
            super(1003, message);
        }
    }
    
    /**
     * 机器不匹配异常
     */
    public static class MachineMismatchException extends LicenseException {
        public MachineMismatchException(String message) {
            super(1004, message);
        }
    }
    
    /**
     * 签名验证失败异常
     */
    public static class SignatureVerifyException extends LicenseException {
        public SignatureVerifyException(String message) {
            super(1005, message);
        }
    }
    
    /**
     * 网络异常
     */
    public static class NetworkException extends LicenseException {
        public NetworkException(String message) {
            super(2001, message);
        }
        
        public NetworkException(String message, Throwable cause) {
            super(2001, message, cause);
        }
    }
    
    /**
     * 服务器异常
     */
    public static class ServerException extends LicenseException {
        public ServerException(String message) {
            super(2002, message);
        }
        
        public ServerException(String message, Throwable cause) {
            super(2002, message, cause);
        }
    }
}
