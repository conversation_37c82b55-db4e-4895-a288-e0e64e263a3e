2025-08-14 18:08:09.736 [main] INFO  c.r.l.s.LicenseServerApplication - Starting LicenseServerApplication using Java 1.8.0_342 on NucBox_M7 with PID 29404 (C:\Users\<USER>\IdeaProjects\study\license-system\license-server\target\classes started by yangfan in C:\Users\<USER>\IdeaProjects\study\license-system)
2025-08-14 18:08:09.741 [main] DEBUG c.r.l.s.LicenseServerApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-14 18:08:09.741 [main] INFO  c.r.l.s.LicenseServerApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-14 18:08:10.550 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-14 18:08:10.619 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 59 ms. Found 1 JPA repository interfaces.
2025-08-14 18:08:11.291 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-14 18:08:11.303 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-14 18:08:11.304 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-14 18:08:11.438 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-14 18:08:11.438 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1641 ms
2025-08-14 18:08:11.489 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-14 18:08:11.777 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-14 18:08:11.793 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:license_db'
2025-08-14 18:08:11.920 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Invocation of init method failed; nested exception is org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #1 of URL [file:/C:/Users/<USER>/IdeaProjects/study/license-system/license-server/target/classes/schema.sql]: CREATE TABLE IF NOT EXISTS license ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID', license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）', customer_id VARCHAR(64) NOT NULL COMMENT '客户ID', customer_name VARCHAR(200) NOT NULL COMMENT '客户名称', product_name VARCHAR(100) NOT NULL COMMENT '产品名称', product_version VARCHAR(50) COMMENT '产品版本', license_type VARCHAR(50) NOT NULL COMMENT '许可证类型', machine_id VARCHAR(200) COMMENT '机器标识', status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态', activation_code VARCHAR(100) UNIQUE COMMENT '激活码', issue_time DATETIME NOT NULL COMMENT '签发时间', effective_time DATETIME NOT NULL COMMENT '生效时间', expire_time DATETIME NOT NULL COMMENT '过期时间', max_users INT COMMENT '最大用户数', max_concurrent INT COMMENT '最大并发数', modules TEXT COMMENT '功能模块列表（JSON格式）', extensions TEXT COMMENT '扩展属性（JSON格式）', signature TEXT COMMENT '数字签名', last_verify_time DATETIME COMMENT '最后验证时间', verify_count BIGINT DEFAULT 0 COMMENT '验证次数', create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', remarks VARCHAR(500) COMMENT '备注信息', INDEX idx_license_id (license_id), INDEX idx_customer_id (customer_id), INDEX idx_machine_id (machine_id), INDEX idx_activation_code (activation_code), INDEX idx_status (status), INDEX idx_expire_time (expire_time), INDEX idx_create_time (create_time), INDEX idx_last_verify_time (last_verify_time) ) COMMENT='许可证表'; nested exception is org.h2.jdbc.JdbcSQLNonTransientException: Unknown data type: "IDX_LICENSE_ID"; SQL statement:
CREATE TABLE IF NOT EXISTS license ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID', license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）', customer_id VARCHAR(64) NOT NULL COMMENT '客户ID', customer_name VARCHAR(200) NOT NULL COMMENT '客户名称', product_name VARCHAR(100) NOT NULL COMMENT '产品名称', product_version VARCHAR(50) COMMENT '产品版本', license_type VARCHAR(50) NOT NULL COMMENT '许可证类型', machine_id VARCHAR(200) COMMENT '机器标识', status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态', activation_code VARCHAR(100) UNIQUE COMMENT '激活码', issue_time DATETIME NOT NULL COMMENT '签发时间', effective_time DATETIME NOT NULL COMMENT '生效时间', expire_time DATETIME NOT NULL COMMENT '过期时间', max_users INT COMMENT '最大用户数', max_concurrent INT COMMENT '最大并发数', modules TEXT COMMENT '功能模块列表（JSON格式）', extensions TEXT COMMENT '扩展属性（JSON格式）', signature TEXT COMMENT '数字签名', last_verify_time DATETIME COMMENT '最后验证时间', verify_count BIGINT DEFAULT 0 COMMENT '验证次数', create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', remarks VARCHAR(500) COMMENT '备注信息', INDEX idx_license_id (license_id), INDEX idx_customer_id (customer_id), INDEX idx_machine_id (machine_id), INDEX idx_activation_code (activation_code), INDEX idx_status (status), INDEX idx_expire_time (expire_time), INDEX idx_create_time (create_time), INDEX idx_last_verify_time (last_verify_time) ) COMMENT='许可证表' [50004-214]
2025-08-14 18:08:11.920 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-14 18:08:11.923 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-14 18:08:11.927 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-14 18:08:11.946 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-14 18:08:11.993 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Invocation of init method failed; nested exception is org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #1 of URL [file:/C:/Users/<USER>/IdeaProjects/study/license-system/license-server/target/classes/schema.sql]: CREATE TABLE IF NOT EXISTS license ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID', license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）', customer_id VARCHAR(64) NOT NULL COMMENT '客户ID', customer_name VARCHAR(200) NOT NULL COMMENT '客户名称', product_name VARCHAR(100) NOT NULL COMMENT '产品名称', product_version VARCHAR(50) COMMENT '产品版本', license_type VARCHAR(50) NOT NULL COMMENT '许可证类型', machine_id VARCHAR(200) COMMENT '机器标识', status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态', activation_code VARCHAR(100) UNIQUE COMMENT '激活码', issue_time DATETIME NOT NULL COMMENT '签发时间', effective_time DATETIME NOT NULL COMMENT '生效时间', expire_time DATETIME NOT NULL COMMENT '过期时间', max_users INT COMMENT '最大用户数', max_concurrent INT COMMENT '最大并发数', modules TEXT COMMENT '功能模块列表（JSON格式）', extensions TEXT COMMENT '扩展属性（JSON格式）', signature TEXT COMMENT '数字签名', last_verify_time DATETIME COMMENT '最后验证时间', verify_count BIGINT DEFAULT 0 COMMENT '验证次数', create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', remarks VARCHAR(500) COMMENT '备注信息', INDEX idx_license_id (license_id), INDEX idx_customer_id (customer_id), INDEX idx_machine_id (machine_id), INDEX idx_activation_code (activation_code), INDEX idx_status (status), INDEX idx_expire_time (expire_time), INDEX idx_create_time (create_time), INDEX idx_last_verify_time (last_verify_time) ) COMMENT='许可证表'; nested exception is org.h2.jdbc.JdbcSQLNonTransientException: Unknown data type: "IDX_LICENSE_ID"; SQL statement:
CREATE TABLE IF NOT EXISTS license ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID', license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）', customer_id VARCHAR(64) NOT NULL COMMENT '客户ID', customer_name VARCHAR(200) NOT NULL COMMENT '客户名称', product_name VARCHAR(100) NOT NULL COMMENT '产品名称', product_version VARCHAR(50) COMMENT '产品版本', license_type VARCHAR(50) NOT NULL COMMENT '许可证类型', machine_id VARCHAR(200) COMMENT '机器标识', status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态', activation_code VARCHAR(100) UNIQUE COMMENT '激活码', issue_time DATETIME NOT NULL COMMENT '签发时间', effective_time DATETIME NOT NULL COMMENT '生效时间', expire_time DATETIME NOT NULL COMMENT '过期时间', max_users INT COMMENT '最大用户数', max_concurrent INT COMMENT '最大并发数', modules TEXT COMMENT '功能模块列表（JSON格式）', extensions TEXT COMMENT '扩展属性（JSON格式）', signature TEXT COMMENT '数字签名', last_verify_time DATETIME COMMENT '最后验证时间', verify_count BIGINT DEFAULT 0 COMMENT '验证次数', create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', remarks VARCHAR(500) COMMENT '备注信息', INDEX idx_license_id (license_id), INDEX idx_customer_id (customer_id), INDEX idx_machine_id (machine_id), INDEX idx_activation_code (activation_code), INDEX idx_status (status), INDEX idx_expire_time (expire_time), INDEX idx_create_time (create_time), INDEX idx_last_verify_time (last_verify_time) ) COMMENT='许可证表' [50004-214]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:919)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.robin.license.server.LicenseServerApplication.main(LicenseServerApplication.java:18)
Caused by: org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #1 of URL [file:/C:/Users/<USER>/IdeaProjects/study/license-system/license-server/target/classes/schema.sql]: CREATE TABLE IF NOT EXISTS license ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID', license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）', customer_id VARCHAR(64) NOT NULL COMMENT '客户ID', customer_name VARCHAR(200) NOT NULL COMMENT '客户名称', product_name VARCHAR(100) NOT NULL COMMENT '产品名称', product_version VARCHAR(50) COMMENT '产品版本', license_type VARCHAR(50) NOT NULL COMMENT '许可证类型', machine_id VARCHAR(200) COMMENT '机器标识', status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态', activation_code VARCHAR(100) UNIQUE COMMENT '激活码', issue_time DATETIME NOT NULL COMMENT '签发时间', effective_time DATETIME NOT NULL COMMENT '生效时间', expire_time DATETIME NOT NULL COMMENT '过期时间', max_users INT COMMENT '最大用户数', max_concurrent INT COMMENT '最大并发数', modules TEXT COMMENT '功能模块列表（JSON格式）', extensions TEXT COMMENT '扩展属性（JSON格式）', signature TEXT COMMENT '数字签名', last_verify_time DATETIME COMMENT '最后验证时间', verify_count BIGINT DEFAULT 0 COMMENT '验证次数', create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', remarks VARCHAR(500) COMMENT '备注信息', INDEX idx_license_id (license_id), INDEX idx_customer_id (customer_id), INDEX idx_machine_id (machine_id), INDEX idx_activation_code (activation_code), INDEX idx_status (status), INDEX idx_expire_time (expire_time), INDEX idx_create_time (create_time), INDEX idx_last_verify_time (last_verify_time) ) COMMENT='许可证表'; nested exception is org.h2.jdbc.JdbcSQLNonTransientException: Unknown data type: "IDX_LICENSE_ID"; SQL statement:
CREATE TABLE IF NOT EXISTS license ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID', license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）', customer_id VARCHAR(64) NOT NULL COMMENT '客户ID', customer_name VARCHAR(200) NOT NULL COMMENT '客户名称', product_name VARCHAR(100) NOT NULL COMMENT '产品名称', product_version VARCHAR(50) COMMENT '产品版本', license_type VARCHAR(50) NOT NULL COMMENT '许可证类型', machine_id VARCHAR(200) COMMENT '机器标识', status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态', activation_code VARCHAR(100) UNIQUE COMMENT '激活码', issue_time DATETIME NOT NULL COMMENT '签发时间', effective_time DATETIME NOT NULL COMMENT '生效时间', expire_time DATETIME NOT NULL COMMENT '过期时间', max_users INT COMMENT '最大用户数', max_concurrent INT COMMENT '最大并发数', modules TEXT COMMENT '功能模块列表（JSON格式）', extensions TEXT COMMENT '扩展属性（JSON格式）', signature TEXT COMMENT '数字签名', last_verify_time DATETIME COMMENT '最后验证时间', verify_count BIGINT DEFAULT 0 COMMENT '验证次数', create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', remarks VARCHAR(500) COMMENT '备注信息', INDEX idx_license_id (license_id), INDEX idx_customer_id (customer_id), INDEX idx_machine_id (machine_id), INDEX idx_activation_code (activation_code), INDEX idx_status (status), INDEX idx_expire_time (expire_time), INDEX idx_create_time (create_time), INDEX idx_last_verify_time (last_verify_time) ) COMMENT='许可证表' [50004-214]
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer.runScripts(DataSourceScriptDatabaseInitializer.java:90)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.runScripts(AbstractScriptDatabaseInitializer.java:145)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applyScripts(AbstractScriptDatabaseInitializer.java:107)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applySchemaScripts(AbstractScriptDatabaseInitializer.java:97)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.initializeDatabase(AbstractScriptDatabaseInitializer.java:75)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.afterPropertiesSet(AbstractScriptDatabaseInitializer.java:65)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 18 common frames omitted
Caused by: org.h2.jdbc.JdbcSQLNonTransientException: Unknown data type: "IDX_LICENSE_ID"; SQL statement:
CREATE TABLE IF NOT EXISTS license ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID', license_id VARCHAR(64) NOT NULL UNIQUE COMMENT '许可证ID（唯一标识）', customer_id VARCHAR(64) NOT NULL COMMENT '客户ID', customer_name VARCHAR(200) NOT NULL COMMENT '客户名称', product_name VARCHAR(100) NOT NULL COMMENT '产品名称', product_version VARCHAR(50) COMMENT '产品版本', license_type VARCHAR(50) NOT NULL COMMENT '许可证类型', machine_id VARCHAR(200) COMMENT '机器标识', status VARCHAR(20) NOT NULL DEFAULT 'VALID' COMMENT '许可证状态', activation_code VARCHAR(100) UNIQUE COMMENT '激活码', issue_time DATETIME NOT NULL COMMENT '签发时间', effective_time DATETIME NOT NULL COMMENT '生效时间', expire_time DATETIME NOT NULL COMMENT '过期时间', max_users INT COMMENT '最大用户数', max_concurrent INT COMMENT '最大并发数', modules TEXT COMMENT '功能模块列表（JSON格式）', extensions TEXT COMMENT '扩展属性（JSON格式）', signature TEXT COMMENT '数字签名', last_verify_time DATETIME COMMENT '最后验证时间', verify_count BIGINT DEFAULT 0 COMMENT '验证次数', create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', remarks VARCHAR(500) COMMENT '备注信息', INDEX idx_license_id (license_id), INDEX idx_customer_id (customer_id), INDEX idx_machine_id (machine_id), INDEX idx_activation_code (activation_code), INDEX idx_status (status), INDEX idx_expire_time (expire_time), INDEX idx_create_time (create_time), INDEX idx_last_verify_time (last_verify_time) ) COMMENT='许可证表' [50004-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:554)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.readIfDataType1(Parser.java:6249)
	at org.h2.command.Parser.readIfDataType(Parser.java:6087)
	at org.h2.command.Parser.parseColumnWithType(Parser.java:6069)
	at org.h2.command.Parser.parseColumnForTable(Parser.java:5948)
	at org.h2.command.Parser.parseTableColumnDefinition(Parser.java:9331)
	at org.h2.command.Parser.parseCreateTable(Parser.java:9271)
	at org.h2.command.Parser.parseCreate(Parser.java:6784)
	at org.h2.command.Parser.parsePrepared(Parser.java:763)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcStatement.executeInternal(JdbcStatement.java:237)
	at org.h2.jdbc.JdbcStatement.execute(JdbcStatement.java:223)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 28 common frames omitted
