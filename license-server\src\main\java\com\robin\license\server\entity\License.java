package com.robin.license.server.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 许可证实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Entity
@Table(name = "license")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class License {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 许可证ID（唯一标识）
     */
    @Column(name = "license_id", unique = true, nullable = false, length = 64)
    private String licenseId;
    
    /**
     * 客户ID
     */
    @Column(name = "customer_id", nullable = false, length = 64)
    private String customerId;
    
    /**
     * 客户名称
     */
    @Column(name = "customer_name", nullable = false, length = 200)
    private String customerName;
    
    /**
     * 产品名称
     */
    @Column(name = "product_name", nullable = false, length = 100)
    private String productName;
    
    /**
     * 产品版本
     */
    @Column(name = "product_version", length = 50)
    private String productVersion;
    
    /**
     * 许可证类型
     */
    @Column(name = "license_type", nullable = false, length = 50)
    private String licenseType;
    
    /**
     * 机器标识
     */
    @Column(name = "machine_id", length = 200)
    private String machineId;
    
    /**
     * 许可证状态
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status;
    
    /**
     * 激活码
     */
    @Column(name = "activation_code", unique = true, length = 100)
    private String activationCode;
    
    /**
     * 签发时间
     */
    @Column(name = "issue_time", nullable = false)
    private LocalDateTime issueTime;
    
    /**
     * 生效时间
     */
    @Column(name = "effective_time", nullable = false)
    private LocalDateTime effectiveTime;
    
    /**
     * 过期时间
     */
    @Column(name = "expire_time", nullable = false)
    private LocalDateTime expireTime;
    
    /**
     * 最大用户数
     */
    @Column(name = "max_users")
    private Integer maxUsers;
    
    /**
     * 最大并发数
     */
    @Column(name = "max_concurrent")
    private Integer maxConcurrent;
    
    /**
     * 功能模块列表（JSON格式）
     */
    @Column(name = "modules", columnDefinition = "TEXT")
    private String modules;
    
    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extensions", columnDefinition = "TEXT")
    private String extensions;
    
    /**
     * 数字签名
     */
    @Column(name = "signature", columnDefinition = "TEXT")
    private String signature;
    
    /**
     * 最后验证时间
     */
    @Column(name = "last_verify_time")
    private LocalDateTime lastVerifyTime;
    
    /**
     * 验证次数
     */
    @Column(name = "verify_count")
    private Long verifyCount;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 备注信息
     */
    @Column(name = "remarks", length = 500)
    private String remarks;
    
    /**
     * 创建时间自动设置
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (verifyCount == null) {
            verifyCount = 0L;
        }
    }
    
    /**
     * 更新时间自动设置
     */
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
    
    /**
     * 检查许可证是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return "VALID".equals(status) || "1".equals(status);
    }
    
    /**
     * 检查许可证是否过期
     * 
     * @return 是否过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查许可证是否在有效期内
     * 
     * @return 是否在有效期内
     */
    public boolean isInValidPeriod() {
        LocalDateTime now = LocalDateTime.now();
        return !now.isBefore(effectiveTime) && !now.isAfter(expireTime);
    }
    
    /**
     * 增加验证次数
     */
    public void incrementVerifyCount() {
        if (verifyCount == null) {
            verifyCount = 0L;
        }
        verifyCount++;
        lastVerifyTime = LocalDateTime.now();
    }
}
