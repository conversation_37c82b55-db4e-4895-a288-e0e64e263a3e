<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="fastRequestCurrentParamGroup">
    <option name="bodyParamMap">
      <map>
        <entry key="request">
          <value>
            <Object />
          </value>
        </entry>
      </map>
    </option>
    <option name="clasDescription" value="许可证REST API控制器" />
    <option name="className" value="com.robin.license.server.controller.LicenseController" />
    <option name="filePath" value="" />
    <option name="method" value="verifyLicense" />
    <option name="methodDescription" value="验证许可证" />
    <option name="methodType" value="POST" />
    <option name="module" value="license-server" />
    <option name="originUrl" value="/api/license/verify" />
    <option name="responseParamMap">
      <map>
        <entry key="return">
          <value>
            <Object />
          </value>
        </entry>
      </map>
    </option>
    <option name="returnDocument" value="{&quot;status&quot;:{},&quot;headers&quot;:{},&quot;body&quot;:{&quot;code&quot;:&quot;响应码&quot;,&quot;message&quot;:&quot;响应消息&quot;,&quot;data&quot;:{&quot;java.lang.Boolean&quot;:&quot;No comment,Type =Boolean&quot;},&quot;timestamp&quot;:&quot;时间戳&quot;,&quot;requestId&quot;:&quot;请求ID&quot;}}" />
    <option name="url" value="/api/license/verify" />
  </component>
</project>