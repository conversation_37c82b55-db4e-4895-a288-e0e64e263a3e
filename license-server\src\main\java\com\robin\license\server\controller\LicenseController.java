package com.robin.license.server.controller;

import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.dto.ApiResponse;
import com.robin.license.common.dto.LicenseFileResponse;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.exception.LicenseException;
import com.robin.license.server.entity.License;
import com.robin.license.server.service.LicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 许可证REST API控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/license")
@Slf4j
@Validated
public class LicenseController {
    
    @Autowired
    private LicenseService licenseService;
    

    /**
     * 许可证同步（统一的注册和验证接口）
     *
     * @param machineInfo 机器信息
     * @param httpRequest HTTP请求
     * @return 许可证文件响应
     */
    @PostMapping("/sync")
    public ResponseEntity<ApiResponse<LicenseFileResponse>> syncLicense(
            @Valid @RequestBody MachineInfo machineInfo,
            HttpServletRequest httpRequest) {

        try {
            log.info("收到许可证同步请求，机器ID: {}, IP: {}",
                    machineInfo.getMachineId(), httpRequest.getRemoteAddr());

            LicenseFileResponse response = licenseService.syncLicense(machineInfo);

            if (response != null) {
                log.info("许可证同步成功: {}", machineInfo.getMachineId());
                return ResponseEntity.ok(ApiResponse.success("许可证同步成功", response));
            } else {
                log.warn("许可证同步失败: {}", machineInfo.getMachineId());
                return ResponseEntity.ok(ApiResponse.error(
                        LicenseConstants.ErrorCode.REGISTER_FAILED, "许可证同步失败"));
            }

        } catch (Exception e) {
            log.error("许可证同步异常: " + machineInfo.getMachineId(), e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "服务器内部错误"));
        }
    }

    /**
     * 获取许可证信息
     *
     * @param licenseId 许可证ID
     * @return 许可证信息
     */
    @GetMapping("/{licenseId}")
    public ResponseEntity<ApiResponse<LicenseInfo>> getLicenseInfo(
            @PathVariable @NotBlank String licenseId) {
        
        try {
            log.info("获取许可证信息: {}", licenseId);
            
            LicenseInfo licenseInfo = licenseService.getLicenseInfo(licenseId);
            
            return ResponseEntity.ok(ApiResponse.success(licenseInfo));
            
        } catch (LicenseException e) {
            log.warn("获取许可证信息失败: {}, 错误: {}", licenseId, e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("获取许可证信息异常: " + licenseId, e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "服务器内部错误"));
        }
    }
    
    /**
     * 生成许可证
     * 
     * @param licenseInfo 许可证信息
     * @return 生成的许可证
     */
    @PostMapping("/generate")
    public ResponseEntity<ApiResponse<License>> generateLicense(
            @Valid @RequestBody LicenseInfo licenseInfo) {
        
        try {
            log.info("生成许可证请求，客户: {}, 产品: {}", 
                    licenseInfo.getCustomerName(), licenseInfo.getProductName());
            
            License license = licenseService.generateLicense(licenseInfo);
            
            log.info("许可证生成成功: {}", license.getLicenseId());
            return ResponseEntity.ok(ApiResponse.success("许可证生成成功", license));
            
        } catch (Exception e) {
            log.error("生成许可证异常", e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "服务器内部错误"));
        }
    }
    
    /**
     * 撤销许可证
     *
     * @param licenseId 许可证ID
     * @return 操作结果
     */
    @PostMapping("/{licenseId}/revoke")
    public ResponseEntity<ApiResponse<String>> revokeLicense(
            @PathVariable @NotBlank String licenseId) {
        
        try {
            log.info("撤销许可证: {}", licenseId);
            
            licenseService.revokeLicense(licenseId);
            
            log.info("许可证撤销成功: {}", licenseId);
            return ResponseEntity.ok(ApiResponse.success("许可证撤销成功"));
            
        } catch (LicenseException e) {
            log.warn("撤销许可证失败: {}, 错误: {}", licenseId, e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("撤销许可证异常: " + licenseId, e);
            return ResponseEntity.ok(ApiResponse.error(
                    LicenseConstants.ErrorCode.SERVER_ERROR, "服务器内部错误"));
        }
    }
    

    
    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    // 内部请求类
    
    public static class VerifyLicenseRequest {
        @NotBlank(message = "许可证ID不能为空")
        private String licenseId;
        
        @NotBlank(message = "机器ID不能为空")
        private String machineId;
        
        private MachineInfo machineInfo;
        private Long timestamp;
        
        // getters and setters
        public String getLicenseId() { return licenseId; }
        public void setLicenseId(String licenseId) { this.licenseId = licenseId; }
        public String getMachineId() { return machineId; }
        public void setMachineId(String machineId) { this.machineId = machineId; }
        public MachineInfo getMachineInfo() { return machineInfo; }
        public void setMachineInfo(MachineInfo machineInfo) { this.machineInfo = machineInfo; }
        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    }
    

    

    
    public static class RenewLicenseRequest {
        private LocalDateTime newExpireTime;
        
        // getters and setters
        public LocalDateTime getNewExpireTime() { return newExpireTime; }
        public void setNewExpireTime(LocalDateTime newExpireTime) { this.newExpireTime = newExpireTime; }
    }
}
