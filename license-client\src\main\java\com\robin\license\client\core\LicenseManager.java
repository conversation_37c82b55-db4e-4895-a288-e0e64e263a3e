package com.robin.license.client.core;

import com.robin.license.client.network.LicenseClient;
import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.exception.LicenseException;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 许可证管理器
 * 负责许可证的验证、缓存和定期检查
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class LicenseManager {
    
    private static final LicenseManager INSTANCE = new LicenseManager();
    
    private LicenseInfo cachedLicenseInfo;
    private LocalDateTime lastCheckTime;
    private LocalDateTime lastOnlineVerifyTime;
    private ScheduledExecutorService scheduler;
    private LicenseClient licenseClient;
    private boolean initialized = false;
    
    private LicenseManager() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     * 
     * @return 许可证管理器实例
     */
    public static LicenseManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 初始化许可证管理器
     * 
     * @param serverUrl 许可证服务器URL
     */
    public synchronized void initialize(String serverUrl) {
        if (initialized) {
            return;
        }
        
        this.licenseClient = new LicenseClient(serverUrl);
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        // 启动定期检查任务
        startPeriodicCheck();
        
        initialized = true;
        log.info("许可证管理器初始化完成，服务器地址: {}", serverUrl);
    }
    
    /**
     * 验证许可证
     * 
     * @return 许可证信息
     * @throws LicenseException 验证失败时抛出异常
     */
    public LicenseInfo validateLicense() throws LicenseException {
        // 如果缓存的许可证信息仍然有效，直接返回
        if (isCachedLicenseValid()) {
            return cachedLicenseInfo;
        }
        
        // 执行本地验证
        LicenseInfo licenseInfo = LicenseValidator.validateLicense();
        
        // 更新缓存
        this.cachedLicenseInfo = licenseInfo;
        this.lastCheckTime = LocalDateTime.now();
        
        // 如果需要在线验证
        if (shouldPerformOnlineVerify()) {
            performOnlineVerify(licenseInfo);
        }
        
        return licenseInfo;
    }
    
    /**
     * 强制在线验证
     * 
     * @return 许可证信息
     * @throws LicenseException 验证失败时抛出异常
     */
    public LicenseInfo forceOnlineValidate() throws LicenseException {
        LicenseInfo licenseInfo = LicenseValidator.validateLicense();
        performOnlineVerify(licenseInfo);
        
        this.cachedLicenseInfo = licenseInfo;
        this.lastCheckTime = LocalDateTime.now();
        this.lastOnlineVerifyTime = LocalDateTime.now();
        
        return licenseInfo;
    }
    
    /**
     * 获取许可证信息（不进行验证）
     * 
     * @return 缓存的许可证信息
     */
    public LicenseInfo getLicenseInfo() {
        return cachedLicenseInfo;
    }
    
    /**
     * 检查许可证是否有效
     * 
     * @return 是否有效
     */
    public boolean isLicenseValid() {
        try {
            validateLicense();
            return true;
        } catch (LicenseException e) {
            log.warn("许可证验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取许可证剩余天数
     * 
     * @return 剩余天数，-1表示已过期，Long.MAX_VALUE表示永久有效
     */
    public long getRemainingDays() {
        if (cachedLicenseInfo != null) {
            return cachedLicenseInfo.getRemainingDays();
        }
        
        try {
            LicenseInfo licenseInfo = validateLicense();
            return licenseInfo.getRemainingDays();
        } catch (LicenseException e) {
            return -1;
        }
    }
    
    /**
     * 关闭许可证管理器
     */
    public synchronized void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        initialized = false;
        log.info("许可证管理器已关闭");
    }
    
    /**
     * 检查缓存的许可证是否仍然有效
     * 
     * @return 是否有效
     */
    private boolean isCachedLicenseValid() {
        if (cachedLicenseInfo == null || lastCheckTime == null) {
            return false;
        }
        
        // 检查缓存时间是否超过检查间隔
        LocalDateTime now = LocalDateTime.now();
        long timeSinceLastCheck = java.time.Duration.between(lastCheckTime, now).toMillis();
        
        if (timeSinceLastCheck > LicenseConstants.Time.LICENSE_CHECK_INTERVAL) {
            return false;
        }
        
        // 检查许可证是否过期
        return !cachedLicenseInfo.isExpired();
    }
    
    /**
     * 是否需要执行在线验证
     * 
     * @return 是否需要在线验证
     */
    private boolean shouldPerformOnlineVerify() {
        if (lastOnlineVerifyTime == null) {
            return true;
        }
        
        LocalDateTime now = LocalDateTime.now();
        long timeSinceLastOnlineVerify = java.time.Duration.between(lastOnlineVerifyTime, now).toMillis();
        
        return timeSinceLastOnlineVerify > LicenseConstants.Time.ONLINE_VERIFY_INTERVAL;
    }
    
    /**
     * 执行在线验证
     * 
     * @param licenseInfo 许可证信息
     * @throws LicenseException 验证失败时抛出异常
     */
    private void performOnlineVerify(LicenseInfo licenseInfo) throws LicenseException {
        if (licenseClient == null) {
            log.warn("许可证客户端未初始化，跳过在线验证");
            return;
        }
        
        try {
            boolean isValid = licenseClient.verifyLicense(licenseInfo);
            if (!isValid) {
                throw new LicenseException.LicenseInvalidException("在线验证失败");
            }
            
            this.lastOnlineVerifyTime = LocalDateTime.now();
            log.info("在线验证成功");
            
        } catch (Exception e) {
            log.error("在线验证失败", e);
            // 在线验证失败不影响本地验证结果，只记录日志
        }
    }
    
    /**
     * 启动定期检查任务
     */
    private void startPeriodicCheck() {
        // 定期检查许可证有效性
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (cachedLicenseInfo != null && cachedLicenseInfo.isExpired()) {
                    log.warn("许可证已过期，过期时间: {}", cachedLicenseInfo.getExpireTime());
                    cachedLicenseInfo = null;
                }
            } catch (Exception e) {
                log.error("定期检查许可证时发生错误", e);
            }
        }, 1, 1, TimeUnit.HOURS);
        
        // 定期在线验证
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (cachedLicenseInfo != null && shouldPerformOnlineVerify()) {
                    performOnlineVerify(cachedLicenseInfo);
                }
            } catch (Exception e) {
                log.error("定期在线验证时发生错误", e);
            }
        }, 1, 6, TimeUnit.HOURS);
    }
}
