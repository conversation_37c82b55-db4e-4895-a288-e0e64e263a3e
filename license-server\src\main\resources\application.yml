server:
  port: 8080

spring:
  application:
    name: license-server

  # MySQL 数据库配置
  datasource:
    url: ***********************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456

  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

logging:
  level:
    com.robin.license: DEBUG
