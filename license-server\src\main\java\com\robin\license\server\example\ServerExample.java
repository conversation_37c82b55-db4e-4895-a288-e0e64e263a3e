package com.robin.license.server.example;

import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.server.entity.License;
import com.robin.license.server.service.LicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务端使用示例
 * 演示如何使用许可证服务进行许可证管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@Slf4j
public class ServerExample implements CommandLineRunner {
    
    @Autowired
    private LicenseService licenseService;
    
    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== 许可证服务器示例 ===");
        
        // 示例1：生成许可证
        generateLicenseExample();
        
        // 示例2：验证许可证
        verifyLicenseExample();
        
        // 示例3：管理许可证
        manageLicenseExample();
    }
    
    /**
     * 示例1：生成许可证
     */
    private void generateLicenseExample() {
        System.out.println("\n--- 生成许可证示例 ---");
        
        try {
            // 构建许可证信息
            LicenseInfo licenseInfo = LicenseInfo.builder()
                    .customerId("CUST-001")
                    .customerName("示例客户公司")
                    .productName("示例产品")
                    .productVersion("1.0.0")
                    .licenseType(LicenseConstants.Type.STANDARD)
                    .effectiveTime(LocalDateTime.now())
                    .expireTime(LocalDateTime.now().plusDays(365)) // 1年有效期
                    .maxUsers(100)
                    .maxConcurrent(50)
                    .modules(new String[]{"基础功能", "高级功能", "报表功能"})
                    .remarks("标准版许可证，包含所有基础功能")
                    .build();
            
            // 生成许可证
            License license = licenseService.generateLicense(licenseInfo);
            
            System.out.println("许可证生成成功:");
            System.out.println("许可证ID: " + license.getLicenseId());
            System.out.println("激活码: " + license.getActivationCode());
            System.out.println("客户名称: " + license.getCustomerName());
            System.out.println("产品名称: " + license.getProductName());
            System.out.println("过期时间: " + license.getExpireTime());
            
        } catch (Exception e) {
            System.err.println("生成许可证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 示例2：验证许可证
     */
    private void verifyLicenseExample() {
        System.out.println("\n--- 验证许可证示例 ---");
        
        try {
            // 模拟客户端验证请求
            String testLicenseId = "LIC-TEST123";
            String testMachineId = "MACHINE-TEST123";
            
            // 验证许可证
            boolean isValid = licenseService.verifyLicense(testLicenseId, testMachineId);
            
            if (isValid) {
                System.out.println("许可证验证通过");
                
                // 获取许可证详细信息
                try {
                    LicenseInfo licenseInfo = licenseService.getLicenseInfo(testLicenseId);
                    System.out.println("许可证详情:");
                    System.out.println("  客户: " + licenseInfo.getCustomerName());
                    System.out.println("  产品: " + licenseInfo.getProductName());
                    System.out.println("  类型: " + licenseInfo.getLicenseType());
                    System.out.println("  状态: " + licenseInfo.getStatus());
                    System.out.println("  剩余天数: " + licenseInfo.getRemainingDays());
                } catch (Exception e) {
                    System.out.println("获取许可证详情失败: " + e.getMessage());
                }
                
            } else {
                System.out.println("许可证验证失败");
            }
            
        } catch (Exception e) {
            System.err.println("验证许可证时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 示例3：管理许可证
     */
    private void manageLicenseExample() {
        System.out.println("\n--- 管理许可证示例 ---");
        
        try {
            // 查询客户的所有许可证
            String customerId = "CUST-001";
            List<LicenseInfo> customerLicenses = licenseService.getCustomerLicenses(customerId);
            
            System.out.println("客户 " + customerId + " 的许可证列表:");
            for (LicenseInfo license : customerLicenses) {
                System.out.println("  - " + license.getLicenseId() + 
                                 " | " + license.getProductName() + 
                                 " | " + license.getStatus() + 
                                 " | 过期: " + license.getExpireTime());
            }
            
            // 续期许可证示例
            if (!customerLicenses.isEmpty()) {
                LicenseInfo firstLicense = customerLicenses.get(0);
                String licenseId = firstLicense.getLicenseId();
                
                System.out.println("\n续期许可证: " + licenseId);
                LocalDateTime newExpireTime = LocalDateTime.now().plusDays(730); // 延长2年
                
                try {
                    licenseService.renewLicense(licenseId, newExpireTime);
                    System.out.println("许可证续期成功，新过期时间: " + newExpireTime);
                } catch (Exception e) {
                    System.out.println("许可证续期失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.err.println("管理许可证时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 批量生成许可证示例
     */
    public void batchGenerateLicenses() {
        System.out.println("\n--- 批量生成许可证示例 ---");
        
        String[] customers = {
            "客户A公司", "客户B公司", "客户C公司"
        };
        
        String[] licenseTypes = {
            LicenseConstants.Type.TRIAL,
            LicenseConstants.Type.STANDARD,
            LicenseConstants.Type.PROFESSIONAL
        };
        
        for (int i = 0; i < customers.length; i++) {
            try {
                LicenseInfo licenseInfo = LicenseInfo.builder()
                        .customerId("CUST-" + String.format("%03d", i + 1))
                        .customerName(customers[i])
                        .productName("企业管理系统")
                        .productVersion("2.0.0")
                        .licenseType(licenseTypes[i % licenseTypes.length])
                        .effectiveTime(LocalDateTime.now())
                        .expireTime(LocalDateTime.now().plusDays(365))
                        .maxUsers(50 * (i + 1))
                        .maxConcurrent(25 * (i + 1))
                        .modules(new String[]{"用户管理", "权限管理", "数据分析"})
                        .remarks("批量生成的许可证 #" + (i + 1))
                        .build();
                
                License license = licenseService.generateLicense(licenseInfo);
                System.out.println("为 " + customers[i] + " 生成许可证: " + license.getLicenseId());
                
            } catch (Exception e) {
                System.err.println("为 " + customers[i] + " 生成许可证失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 许可证统计示例
     */
    public void licenseStatisticsExample() {
        System.out.println("\n--- 许可证统计示例 ---");
        
        try {
            // 这里需要在LicenseService中添加统计方法
            System.out.println("许可证统计信息:");
            System.out.println("  总许可证数: [需要实现统计方法]");
            System.out.println("  有效许可证数: [需要实现统计方法]");
            System.out.println("  过期许可证数: [需要实现统计方法]");
            System.out.println("  试用版许可证数: [需要实现统计方法]");
            System.out.println("  标准版许可证数: [需要实现统计方法]");
            System.out.println("  专业版许可证数: [需要实现统计方法]");
            
        } catch (Exception e) {
            System.err.println("获取许可证统计信息失败: " + e.getMessage());
        }
    }
}
