# License Management System

一个完整的分布式软件授权管理系统，支持许可证生成、验证、管理和监控。

## 系统架构

```
license-system/
├── license-common/     # 公共组件模块
├── license-client/     # 客户端模块  
├── license-server/     # 服务端模块
└── README.md          # 项目说明
```

## 核心功能

### 🔐 安全特性
- **RSA数字签名**：确保许可证完整性和真实性
- **AES加密**：保护敏感许可证数据
- **机器指纹绑定**：防止许可证非法转移
- **多重验证**：本地验证 + 在线验证

### 📱 客户端功能
- **跨平台支持**：Windows、Linux、macOS
- **机器信息收集**：CPU、主板、硬盘序列号等
- **自动化授权**：自动获取机器码，向服务端注册并获取许可证
- **本地许可证验证**：离线验证支持，每5分钟自动检查
- **在线验证**：与服务端实时通信，每1小时自动同步
- **严格模式**：许可证验证失败时自动退出应用
- **许可证管理**：缓存、定期检查、自动续期提醒

### 🖥️ 服务端功能
- **许可证生成**：支持多种许可证类型
- **自动机器注册**：接收机器码并自动创建试用许可证
- **激活管理**：激活码生成和验证
- **客户管理**：客户信息和许可证关联
- **统计监控**：许可证使用情况统计
- **REST API**：完整的Web服务接口

## 快速开始

### 环境要求
- Java 8+
- Maven 3.6+
- MySQL 5.7+ (可选，默认使用H2)

### 编译项目
```bash
cd license-system
mvn clean compile
```

### 启动服务端
```bash
cd license-server
mvn spring-boot:run
```

### 客户端集成示例

```java
public class MyApplication {
    public static void main(String[] args) {
        // 自动化许可证SDK - 一行代码搞定所有授权流程
        LicenseSDK sdk = LicenseSDK.getInstance();
        sdk.autoInitialize("http://localhost:8080", true);

        // SDK自动完成：获取机器码 → 服务端注册 → 下载证书 → 定时验证
        // 验证失败自动退出应用

        // 业务代码...
        if (sdk.isLicenseValid()) {
            System.out.println("许可证有效，应用正常运行");
        }
    }
}
```

### 服务端许可证生成示例

```java
// 构建许可证信息
LicenseInfo licenseInfo = LicenseInfo.builder()
    .customerId("CUST-001")
    .customerName("示例客户公司")
    .productName("企业管理系统")
    .licenseType("STANDARD")
    .effectiveTime(LocalDateTime.now())
    .expireTime(LocalDateTime.now().plusDays(365))
    .maxUsers(100)
    .modules(new String[]{"用户管理", "报表功能"})
    .build();

// 生成许可证
License license = licenseService.generateLicense(licenseInfo);
System.out.println("激活码: " + license.getActivationCode());
```

## 许可证文件格式

许可证文件采用二进制格式，包含以下结构：
```
[MZ Header] + [加密数据长度] + [加密数据] + [填充字节] + [签名长度] + [数字签名]
```

- **MZ Header**：伪装成可执行文件头
- **加密数据**：使用AES加密的许可证信息
- **数字签名**：使用RSA私钥签名，确保完整性

## 配置说明

### 客户端配置

```java
// 自动化SDK配置
LicenseSDK sdk = LicenseSDK.getInstance();
sdk.autoInitialize("http://your-license-server:8080", true);

// 许可证文件自动存储到：用户目录/.license/
```

### 服务端配置
```properties
# 数据库配置
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=password

# 服务端口
server.port=8080

# 密钥存储路径
license.key.private-key-path=/path/to/private.key
license.key.public-key-path=/path/to/public.key
```

## API接口

### 许可证验证
```http
POST /api/license/verify
Content-Type: application/json

{
  "licenseId": "LIC-123456",
  "machineId": "MACHINE-789",
  "machineInfo": { ... }
}
```

### 许可证激活
```http
POST /api/license/activate
Content-Type: application/json

{
  "activationCode": "ACTIVATION-CODE-123",
  "machineInfo": { ... }
}
```

### 许可证同步
```http
POST /api/license/sync
Content-Type: application/json

{
  "machineId": "MACHINE-789",
  "cpuId": "CPU-123",
  "motherboardId": "MB-456",
  "diskId": "DISK-789"
}
```

## 许可证类型

- **TRIAL**：试用版，功能和时间受限
- **STANDARD**：标准版，基础功能完整
- **PROFESSIONAL**：专业版，包含高级功能
- **ENTERPRISE**：企业版，无功能限制

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 许可证不存在 |
| 1002 | 许可证无效 |
| 1003 | 许可证过期 |
| 1004 | 机器不匹配 |
| 1005 | 签名验证失败 |
| 2001 | 网络错误 |
| 2002 | 服务器错误 |

## 最佳实践

1. **应用启动时验证**：在应用程序启动时进行许可证验证
2. **定期检查**：使用LicenseManager的自动检查功能
3. **优雅降级**：许可证问题时提供试用模式或功能限制
4. **用户提醒**：许可证即将过期时提醒用户续期
5. **日志记录**：记录许可证验证和使用情况

## 安全建议

1. **保护私钥**：服务端私钥必须安全存储
2. **HTTPS通信**：生产环境使用HTTPS
3. **定期更新**：定期更新密钥对
4. **监控异常**：监控异常的验证请求
5. **备份数据**：定期备份许可证数据库

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
