package com.robin.license.common.crypto;

import com.robin.license.common.constants.LicenseConstants;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;
import org.bouncycastle.util.io.pem.PemWriter;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * 加密工具类
 * 提供AES对称加密和RSA非对称加密功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class CryptoUtil {
    
    static {
        // 添加BouncyCastle安全提供者
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }
    
    /**
     * AES加密
     * 
     * @param data 待加密数据
     * @param key 密钥
     * @return 加密后的数据
     */
    public static byte[] encryptAES(byte[] data, byte[] key) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(key, LicenseConstants.Crypto.AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(LicenseConstants.Crypto.AES_CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            return cipher.doFinal(data);
        } catch (Exception e) {
            log.error("AES加密失败", e);
            throw new RuntimeException("AES加密失败", e);
        }
    }
    
    /**
     * AES解密
     * 
     * @param encryptedData 加密数据
     * @param key 密钥
     * @return 解密后的数据
     */
    public static byte[] decryptAES(byte[] encryptedData, byte[] key) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(key, LicenseConstants.Crypto.AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(LicenseConstants.Crypto.AES_CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            return cipher.doFinal(encryptedData);
        } catch (Exception e) {
            log.error("AES解密失败", e);
            throw new RuntimeException("AES解密失败", e);
        }
    }
    
    /**
     * 生成RSA密钥对
     * 
     * @param keySize 密钥长度
     * @return 密钥对
     */
    public static KeyPair generateRSAKeyPair(int keySize) {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance(LicenseConstants.Crypto.RSA_ALGORITHM);
            keyGen.initialize(keySize);
            return keyGen.generateKeyPair();
        } catch (Exception e) {
            log.error("生成RSA密钥对失败", e);
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }
    
    /**
     * RSA私钥签名
     * 
     * @param data 待签名数据
     * @param privateKey 私钥
     * @return 签名
     */
    public static byte[] signRSA(byte[] data, PrivateKey privateKey) {
        try {
            Signature signature = Signature.getInstance(LicenseConstants.Crypto.RSA_SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(data);
            return signature.sign();
        } catch (Exception e) {
            log.error("RSA签名失败", e);
            throw new RuntimeException("RSA签名失败", e);
        }
    }
    
    /**
     * RSA公钥验证签名
     * 
     * @param data 原始数据
     * @param signatureBytes 签名
     * @param publicKey 公钥
     * @return 验证结果
     */
    public static boolean verifyRSA(byte[] data, byte[] signatureBytes, PublicKey publicKey) {
        try {
            Signature signature = Signature.getInstance(LicenseConstants.Crypto.RSA_SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(data);
            return signature.verify(signatureBytes);
        } catch (Exception e) {
            log.error("RSA签名验证失败", e);
            return false;
        }
    }
    
    /**
     * 从PEM格式字符串读取私钥
     * 
     * @param pemString PEM格式私钥字符串
     * @return 私钥对象
     */
    public static PrivateKey readPrivateKeyFromPem(String pemString) {
        try (StringReader stringReader = new StringReader(pemString);
             PemReader pemReader = new PemReader(stringReader)) {
            
            PemObject pemObject = pemReader.readPemObject();
            byte[] keyBytes = pemObject.getContent();
            
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(LicenseConstants.Crypto.RSA_ALGORITHM);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("读取PEM私钥失败", e);
            throw new RuntimeException("读取PEM私钥失败", e);
        }
    }
    
    /**
     * 从PEM格式字符串读取公钥
     * 
     * @param pemString PEM格式公钥字符串
     * @return 公钥对象
     */
    public static PublicKey readPublicKeyFromPem(String pemString) {
        try (StringReader stringReader = new StringReader(pemString);
             PemReader pemReader = new PemReader(stringReader)) {
            
            PemObject pemObject = pemReader.readPemObject();
            byte[] keyBytes = pemObject.getContent();
            
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(LicenseConstants.Crypto.RSA_ALGORITHM);
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error("读取PEM公钥失败", e);
            throw new RuntimeException("读取PEM公钥失败", e);
        }
    }
    
    /**
     * 将私钥转换为PEM格式字符串
     * 
     * @param privateKey 私钥
     * @return PEM格式字符串
     */
    public static String writePrivateKeyToPem(PrivateKey privateKey) {
        try (StringWriter stringWriter = new StringWriter();
             PemWriter pemWriter = new PemWriter(stringWriter)) {
            
            PemObject pemObject = new PemObject("PRIVATE KEY", privateKey.getEncoded());
            pemWriter.writeObject(pemObject);
            pemWriter.flush();
            return stringWriter.toString();
        } catch (Exception e) {
            log.error("写入PEM私钥失败", e);
            throw new RuntimeException("写入PEM私钥失败", e);
        }
    }
    
    /**
     * 将公钥转换为PEM格式字符串
     * 
     * @param publicKey 公钥
     * @return PEM格式字符串
     */
    public static String writePublicKeyToPem(PublicKey publicKey) {
        try (StringWriter stringWriter = new StringWriter();
             PemWriter pemWriter = new PemWriter(stringWriter)) {
            
            PemObject pemObject = new PemObject("PUBLIC KEY", publicKey.getEncoded());
            pemWriter.writeObject(pemObject);
            pemWriter.flush();
            return stringWriter.toString();
        } catch (Exception e) {
            log.error("写入PEM公钥失败", e);
            throw new RuntimeException("写入PEM公钥失败", e);
        }
    }
    
    /**
     * Base64编码
     * 
     * @param data 原始数据
     * @return Base64编码字符串
     */
    public static String base64Encode(byte[] data) {
        return Base64.getEncoder().encodeToString(data);
    }
    
    /**
     * Base64解码
     * 
     * @param base64String Base64编码字符串
     * @return 原始数据
     */
    public static byte[] base64Decode(String base64String) {
        return Base64.getDecoder().decode(base64String);
    }
}
