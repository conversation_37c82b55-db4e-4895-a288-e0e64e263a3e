package com.robin.license.client.example;

import com.robin.license.client.core.LicenseManager;
import com.robin.license.client.hardware.MachineInfoCollector;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.exception.LicenseException;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户端使用示例
 * 演示如何在应用程序中集成许可证验证
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class ClientExample {
    
    public static void main(String[] args) {
        // 示例1：基本许可证验证
        basicLicenseValidation();
        
        // 示例2：使用许可证管理器
        licenseManagerExample();
        
        // 示例3：机器信息收集
        machineInfoExample();
        
        // 示例4：应用程序集成示例
        applicationIntegrationExample();
    }
    
    /**
     * 示例1：基本许可证验证
     */
    private static void basicLicenseValidation() {
        System.out.println("=== 基本许可证验证示例 ===");
        
        try {
            // 初始化许可证管理器
            LicenseManager licenseManager = LicenseManager.getInstance();
            licenseManager.initialize("http://localhost:8080");
            
            // 验证许可证
            LicenseInfo licenseInfo = licenseManager.validateLicense();
            
            System.out.println("许可证验证成功！");
            System.out.println("客户名称: " + licenseInfo.getCustomerName());
            System.out.println("产品名称: " + licenseInfo.getProductName());
            System.out.println("许可证类型: " + licenseInfo.getLicenseType());
            System.out.println("过期时间: " + licenseInfo.getExpireTime());
            System.out.println("剩余天数: " + licenseInfo.getRemainingDays());
            
        } catch (LicenseException e) {
            System.err.println("许可证验证失败: " + e.getMessage());
            System.err.println("错误码: " + e.getErrorCode());
            
            // 根据不同的错误类型进行处理
            if (e instanceof LicenseException.LicenseNotFoundException) {
                System.err.println("请联系供应商获取许可证文件");
            } else if (e instanceof LicenseException.LicenseExpiredException) {
                System.err.println("请联系供应商续期许可证");
            } else if (e instanceof LicenseException.MachineMismatchException) {
                System.err.println("许可证与当前机器不匹配");
            }
        }
    }
    
    /**
     * 示例2：使用许可证管理器
     */
    private static void licenseManagerExample() {
        System.out.println("\n=== 许可证管理器示例 ===");
        
        LicenseManager licenseManager = LicenseManager.getInstance();
        
        try {
            // 初始化（通常在应用启动时执行一次）
            licenseManager.initialize("http://localhost:8080");
            
            // 检查许可证是否有效
            if (licenseManager.isLicenseValid()) {
                System.out.println("许可证有效");
                
                // 获取许可证信息
                LicenseInfo info = licenseManager.getLicenseInfo();
                if (info != null) {
                    System.out.println("许可证类型: " + info.getLicenseType());
                    System.out.println("剩余天数: " + licenseManager.getRemainingDays());
                }
                
                // 强制在线验证
                try {
                    LicenseInfo onlineInfo = licenseManager.forceOnlineValidate();
                    System.out.println("在线验证成功: " + onlineInfo.getCustomerName());
                } catch (LicenseException e) {
                    System.out.println("在线验证失败，但本地验证通过: " + e.getMessage());
                }
                
            } else {
                System.err.println("许可证无效或已过期");
            }
            
        } catch (Exception e) {
            System.err.println("许可证管理器操作失败: " + e.getMessage());
        } finally {
            // 应用关闭时清理资源
            licenseManager.shutdown();
        }
    }
    
    /**
     * 示例3：机器信息收集
     */
    private static void machineInfoExample() {
        System.out.println("\n=== 机器信息收集示例 ===");
        
        try {
            // 收集机器信息
            MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();
            
            System.out.println("机器信息收集完成:");
            System.out.println("机器ID: " + machineInfo.getMachineId());
            System.out.println("CPU序列号: " + machineInfo.getCpuSerial());
            System.out.println("主板序列号: " + machineInfo.getMotherboardSerial());
            System.out.println("硬盘序列号: " + machineInfo.getDiskSerial());
            System.out.println("操作系统: " + machineInfo.getOsName());
            System.out.println("主机名: " + machineInfo.getHostname());
            System.out.println("IP地址: " + machineInfo.getIpAddress());
            System.out.println("MAC地址: " + machineInfo.getMacAddress());
            System.out.println("机器指纹: " + machineInfo.getFingerprint());
            
        } catch (Exception e) {
            System.err.println("机器信息收集失败: " + e.getMessage());
        }
    }
    
    /**
     * 示例4：应用程序集成示例
     */
    private static void applicationIntegrationExample() {
        System.out.println("\n=== 应用程序集成示例 ===");
        
        // 模拟应用程序启动
        MyApplication app = new MyApplication();
        app.startup();
        
        // 模拟应用程序运行
        app.doSomeBusiness();
        
        // 模拟应用程序关闭
        app.shutdown();
    }
    
    /**
     * 示例应用程序类
     */
    static class MyApplication {
        private LicenseManager licenseManager;
        private boolean licenseValid = false;
        
        /**
         * 应用启动
         */
        public void startup() {
            System.out.println("应用程序启动中...");
            
            try {
                // 初始化许可证管理器
                licenseManager = LicenseManager.getInstance();
                licenseManager.initialize("http://localhost:8080");
                
                // 验证许可证
                LicenseInfo licenseInfo = licenseManager.validateLicense();
                licenseValid = true;
                
                System.out.println("许可证验证通过，应用启动成功");
                System.out.println("授权给: " + licenseInfo.getCustomerName());
                
                // 检查功能模块权限
                if (licenseInfo.getModules() != null) {
                    System.out.println("可用功能模块: " + String.join(", ", licenseInfo.getModules()));
                }
                
                // 检查用户数限制
                if (licenseInfo.getMaxUsers() != null) {
                    System.out.println("最大用户数: " + licenseInfo.getMaxUsers());
                }
                
            } catch (LicenseException e) {
                System.err.println("许可证验证失败，应用无法启动: " + e.getMessage());
                licenseValid = false;
                
                // 根据业务需求决定是否允许试用模式
                if (e instanceof LicenseException.LicenseNotFoundException) {
                    System.out.println("启动试用模式（功能受限）");
                    // 可以设置试用模式的限制
                } else {
                    System.exit(1); // 严格模式：许可证问题直接退出
                }
            }
        }
        
        /**
         * 业务逻辑执行
         */
        public void doSomeBusiness() {
            if (!licenseValid) {
                System.out.println("试用模式：功能受限");
                return;
            }
            
            System.out.println("执行业务逻辑...");
            
            // 定期检查许可证状态
            if (!licenseManager.isLicenseValid()) {
                System.err.println("许可证状态异常，停止业务处理");
                return;
            }
            
            // 检查剩余天数，提醒用户续期
            long remainingDays = licenseManager.getRemainingDays();
            if (remainingDays > 0 && remainingDays <= 30) {
                System.out.println("提醒：许可证将在 " + remainingDays + " 天后过期，请及时续期");
            }
            
            System.out.println("业务逻辑执行完成");
        }
        
        /**
         * 应用关闭
         */
        public void shutdown() {
            System.out.println("应用程序关闭中...");
            
            if (licenseManager != null) {
                licenseManager.shutdown();
            }
            
            System.out.println("应用程序已关闭");
        }
    }
}
