package com.robin.license.client.network;

import com.google.gson.Gson;
import com.robin.license.client.hardware.MachineInfoCollector;
import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.dto.ApiResponse;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.exception.LicenseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 许可证网络客户端
 * 负责与许可证服务器的网络通信
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class LicenseClient {
    
    private final String serverUrl;
    private final CloseableHttpClient httpClient;
    private final Gson gson;
    private final RequestConfig requestConfig;
    
    /**
     * 构造函数
     * 
     * @param serverUrl 服务器URL
     */
    public LicenseClient(String serverUrl) {
        this.serverUrl = serverUrl.endsWith("/") ? serverUrl.substring(0, serverUrl.length() - 1) : serverUrl;
        this.gson = new Gson();
        
        // 配置HTTP客户端
        this.requestConfig = RequestConfig.custom()
                .setConnectTimeout(LicenseConstants.Http.DEFAULT_TIMEOUT)
                .setSocketTimeout(LicenseConstants.Http.DEFAULT_TIMEOUT)
                .setConnectionRequestTimeout(LicenseConstants.Http.DEFAULT_TIMEOUT)
                .build();
        
        this.httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();
        
        log.info("许可证客户端初始化完成，服务器地址: {}", this.serverUrl);
    }
    
    /**
     * 验证许可证
     * 
     * @param licenseInfo 许可证信息
     * @return 验证结果
     * @throws LicenseException 验证失败时抛出异常
     */
    public boolean verifyLicense(LicenseInfo licenseInfo) throws LicenseException {
        try {
            // 收集机器信息
            MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("licenseId", licenseInfo.getLicenseId());
            requestData.put("machineId", licenseInfo.getMachineId());
            requestData.put("machineInfo", machineInfo);
            requestData.put("timestamp", System.currentTimeMillis());
            
            // 发送验证请求
            ApiResponse<Boolean> response = sendPostRequest(
                    LicenseConstants.Http.VERIFY_PATH, 
                    requestData, 
                    Boolean.class
            );
            
            if (response.isSuccess()) {
                return response.getData() != null && response.getData();
            } else {
                throw new LicenseException.ServerException("服务器验证失败: " + response.getMessage());
            }
            
        } catch (LicenseException e) {
            throw e;
        } catch (Exception e) {
            log.error("许可证验证网络请求失败", e);
            throw new LicenseException.NetworkException("网络验证失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 激活许可证
     * 
     * @param activationCode 激活码
     * @return 许可证信息
     * @throws LicenseException 激活失败时抛出异常
     */
    public LicenseInfo activateLicense(String activationCode) throws LicenseException {
        try {
            // 收集机器信息
            MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("activationCode", activationCode);
            requestData.put("machineInfo", machineInfo);
            requestData.put("timestamp", System.currentTimeMillis());
            
            // 发送激活请求
            ApiResponse<LicenseInfo> response = sendPostRequest(
                    LicenseConstants.Http.ACTIVATE_PATH, 
                    requestData, 
                    LicenseInfo.class
            );
            
            if (response.isSuccess()) {
                return response.getData();
            } else {
                throw new LicenseException.ServerException("许可证激活失败: " + response.getMessage());
            }
            
        } catch (LicenseException e) {
            throw e;
        } catch (Exception e) {
            log.error("许可证激活网络请求失败", e);
            throw new LicenseException.NetworkException("网络激活失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 发送心跳请求
     * 
     * @param licenseInfo 许可证信息
     * @return 心跳响应
     * @throws LicenseException 心跳失败时抛出异常
     */
    public boolean sendHeartbeat(LicenseInfo licenseInfo) throws LicenseException {
        try {
            // 收集机器信息
            MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("licenseId", licenseInfo.getLicenseId());
            requestData.put("machineId", licenseInfo.getMachineId());
            requestData.put("machineInfo", machineInfo);
            requestData.put("timestamp", System.currentTimeMillis());
            
            // 发送心跳请求
            ApiResponse<Boolean> response = sendPostRequest(
                    LicenseConstants.Http.HEARTBEAT_PATH, 
                    requestData, 
                    Boolean.class
            );
            
            if (response.isSuccess()) {
                return response.getData() != null && response.getData();
            } else {
                log.warn("心跳请求失败: {}", response.getMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("心跳请求网络失败", e);
            return false;
        }
    }
    
    /**
     * 发送POST请求
     * 
     * @param path 请求路径
     * @param requestData 请求数据
     * @param responseType 响应数据类型
     * @param <T> 响应数据类型
     * @return API响应
     * @throws IOException 网络异常
     */
    @SuppressWarnings("unchecked")
    private <T> ApiResponse<T> sendPostRequest(String path, Object requestData, Class<T> responseType) 
            throws IOException {
        
        String url = serverUrl + path;
        HttpPost httpPost = new HttpPost(url);
        
        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("User-Agent", "LicenseClient/1.0.0");
        
        // 设置请求体
        String jsonData = gson.toJson(requestData);
        StringEntity entity = new StringEntity(jsonData, StandardCharsets.UTF_8);
        httpPost.setEntity(entity);
        
        log.debug("发送请求到: {}, 数据: {}", url, jsonData);
        
        // 执行请求
        HttpResponse response = httpClient.execute(httpPost);
        HttpEntity responseEntity = response.getEntity();
        
        if (responseEntity != null) {
            String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
            log.debug("收到响应: {}", responseBody);
            
            // 解析响应
            if (responseType == Boolean.class) {
                return gson.fromJson(responseBody, ApiResponse.class);
            } else if (responseType == LicenseInfo.class) {
                return gson.fromJson(responseBody, ApiResponse.class);
            } else {
                return gson.fromJson(responseBody, ApiResponse.class);
            }
        }
        
        return ApiResponse.error(LicenseConstants.ErrorCode.NETWORK_ERROR, "响应为空");
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            log.error("关闭HTTP客户端失败", e);
        }
    }
}
