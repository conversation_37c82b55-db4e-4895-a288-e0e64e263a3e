package com.robin.license.common.constants;

/**
 * 许可证系统常量定义
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class LicenseConstants {
    
    /**
     * 许可证状态
     */
    public static final class Status {
        /** 有效状态 */
        public static final String VALID = "1";
        /** 无效状态 */
        public static final String INVALID = "0";
        /** 过期状态 */
        public static final String EXPIRED = "2";
        /** 锁定状态 */
        public static final String LOCKED = "3";
    }
    
    /**
     * 许可证类型
     */
    public static final class Type {
        /** 试用版 */
        public static final String TRIAL = "TRIAL";
        /** 标准版 */
        public static final String STANDARD = "STANDARD";
        /** 专业版 */
        public static final String PROFESSIONAL = "PROFESSIONAL";
        /** 企业版 */
        public static final String ENTERPRISE = "ENTERPRISE";
    }
    
    /**
     * 加密算法常量
     */
    public static final class Crypto {
        /** AES算法 */
        public static final String AES_ALGORITHM = "AES";
        /** AES加密模式 */
        public static final String AES_CIPHER_ALGORITHM = "AES/ECB/PKCS7Padding";
        /** RSA算法 */
        public static final String RSA_ALGORITHM = "RSA";
        /** RSA签名算法 */
        public static final String RSA_SIGNATURE_ALGORITHM = "SHA256WithRSA";
        /** 默认密钥 */
        public static final String DEFAULT_KEY = "Robin1234567@123";
    }
    
    /**
     * 文件常量
     */
    public static final class File {
        /** 许可证文件名 */
        public static final String LICENSE_FILE = "license.dat";
        /** 公钥文件名 */
        public static final String PUBLIC_KEY_FILE = "public.key";
        /** 私钥文件名 */
        public static final String PRIVATE_KEY_FILE = "private.key";
        /** 许可证目录名 */
        public static final String LICENSE_DIR = ".license";
        /** 备份文件后缀 */
        public static final String BACKUP_SUFFIX = ".bak";
    }
    
    /**
     * 字节常量
     */
    public static final class Bytes {
        /** 数据填充字节 */
        public static final byte[] DATA_PADDING = {0x7F};
        /** 文件结束标记 */
        public static final byte[] FILE_ENDING = {0x00};
        /** 分隔符字节 */
        public static final byte[] SEPARATOR = {0x7F, 0x7F};
        /** EXE文件头标识 */
        public static final byte[] MZ_HEADER = {
            0x4D, 0x5A, 0x50, 0x00, 0x02, 0x00, 0x00, 0x00, 
            0x04, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 
            0x00, 0x00, 0x00
        };
    }
    
    /**
     * 时间常量
     */
    public static final class Time {
        /** 默认试用天数 */
        public static final int DEFAULT_TRIAL_DAYS = 7;
        /** 本地许可证检查间隔（毫秒） - 每5分钟 */
        public static final long LOCAL_CHECK_INTERVAL = 5 * 60 * 1000L; // 5分钟
        /** 在线验证间隔（毫秒） - 每1小时 */
        public static final long ONLINE_VERIFY_INTERVAL = 60 * 60 * 1000L; // 1小时
        /** 许可证检查间隔（毫秒） - 兼容旧版本 */
        public static final long LICENSE_CHECK_INTERVAL = LOCAL_CHECK_INTERVAL;
    }
    
    /**
     * 系统属性常量
     */
    public static final class System {
        /** 用户目录属性 */
        public static final String USER_HOME = "user.home";
        /** 操作系统名称属性 */
        public static final String OS_NAME = "os.name";
        /** 系统架构属性 */
        public static final String OS_ARCH = "os.arch";
    }
    
    /**
     * HTTP相关常量
     */
    public static final class Http {
        /** 验证接口路径 */
        public static final String VERIFY_PATH = "/api/license/verify";
        /** 激活接口路径 */
        public static final String ACTIVATE_PATH = "/api/license/activate";
        /** 许可证同步接口路径（统一的注册和验证接口） */
        public static final String SYNC_PATH = "/api/license/sync";
        /** 默认超时时间（毫秒） */
        public static final int DEFAULT_TIMEOUT = 30000;
    }
    
    /**
     * 错误码常量
     */
    public static final class ErrorCode {
        /** 成功 */
        public static final int SUCCESS = 0;
        /** 许可证不存在 */
        public static final int LICENSE_NOT_FOUND = 1001;
        /** 许可证无效 */
        public static final int LICENSE_INVALID = 1002;
        /** 许可证过期 */
        public static final int LICENSE_EXPIRED = 1003;
        /** 机器不匹配 */
        public static final int MACHINE_MISMATCH = 1004;
        /** 签名验证失败 */
        public static final int SIGNATURE_VERIFY_FAILED = 1005;
        /** 机器注册失败 */
        public static final int REGISTER_FAILED = 1006;
        /** 网络错误 */
        public static final int NETWORK_ERROR = 2001;
        /** 服务器错误 */
        public static final int SERVER_ERROR = 2002;
    }
}
