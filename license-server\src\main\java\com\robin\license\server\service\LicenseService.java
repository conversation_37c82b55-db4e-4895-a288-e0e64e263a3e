package com.robin.license.server.service;

import com.google.gson.Gson;
import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.crypto.CryptoUtil;
import com.robin.license.common.dto.LicenseFileResponse;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.exception.LicenseException;
import com.robin.license.server.entity.License;
import com.robin.license.server.repository.LicenseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 许可证服务类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Slf4j
public class LicenseService {
    
    @Autowired
    private LicenseRepository licenseRepository;
    
    @Autowired
    private KeyManagementService keyManagementService;
    
    private final Gson gson = new Gson();
    
    /**
     * 生成许可证
     * 
     * @param licenseInfo 许可证信息
     * @return 生成的许可证
     */
    @Transactional
    public License generateLicense(LicenseInfo licenseInfo) {
        // 生成唯一的许可证ID
        String licenseId = generateLicenseId();
        
        // 生成激活码
        String activationCode = generateActivationCode();
        
        // 创建许可证实体
        License license = License.builder()
                .licenseId(licenseId)
                .customerId(licenseInfo.getCustomerId())
                .customerName(licenseInfo.getCustomerName())
                .productName(licenseInfo.getProductName())
                .productVersion(licenseInfo.getProductVersion())
                .licenseType(licenseInfo.getLicenseType())
                .machineId(licenseInfo.getMachineId())
                .status(LicenseConstants.Status.VALID)
                .activationCode(activationCode)
                .issueTime(LocalDateTime.now())
                .effectiveTime(licenseInfo.getEffectiveTime())
                .expireTime(licenseInfo.getExpireTime())
                .maxUsers(licenseInfo.getMaxUsers())
                .maxConcurrent(licenseInfo.getMaxConcurrent())
                .modules(gson.toJson(licenseInfo.getModules()))
                .extensions(gson.toJson(licenseInfo.getExtensions()))
                .remarks(licenseInfo.getRemarks())
                .build();
        
        // 生成数字签名
        String signature = generateSignature(license);
        license.setSignature(signature);
        
        // 保存许可证
        License savedLicense = licenseRepository.save(license);
        
        log.info("生成许可证成功，许可证ID: {}, 客户: {}, 产品: {}", 
                licenseId, licenseInfo.getCustomerName(), licenseInfo.getProductName());
        
        return savedLicense;
    }
    
    /**
     * 激活许可证
     * 
     * @param activationCode 激活码
     * @param machineInfo 机器信息
     * @return 许可证信息
     */
    @Transactional
    public LicenseInfo activateLicense(String activationCode, MachineInfo machineInfo) {
        // 查找许可证
        Optional<License> licenseOpt = licenseRepository.findByActivationCode(activationCode);
        if (!licenseOpt.isPresent()) {
            throw new LicenseException.LicenseNotFoundException("激活码无效: " + activationCode);
        }
        
        License license = licenseOpt.get();
        
        // 检查许可证状态
        if (!license.isValid()) {
            throw new LicenseException.LicenseInvalidException("许可证状态无效");
        }
        
        // 检查是否已过期
        if (license.isExpired()) {
            throw new LicenseException.LicenseExpiredException("许可证已过期");
        }
        
        // 绑定机器
        if (license.getMachineId() == null) {
            license.setMachineId(machineInfo.getFingerprint());
        } else if (!license.getMachineId().equals(machineInfo.getFingerprint())) {
            throw new LicenseException.MachineMismatchException("许可证已绑定到其他机器");
        }
        
        // 更新激活信息
        license.incrementVerifyCount();
        licenseRepository.save(license);
        
        log.info("许可证激活成功，许可证ID: {}, 机器ID: {}", license.getLicenseId(), machineInfo.getFingerprint());
        
        return convertToLicenseInfo(license);
    }
    
    /**
     * 验证许可证
     * 
     * @param licenseId 许可证ID
     * @param machineId 机器ID
     * @return 验证结果
     */
    @Transactional
    public boolean verifyLicense(String licenseId, String machineId) {
        try {
            Optional<License> licenseOpt = licenseRepository.findByLicenseId(licenseId);
            if (!licenseOpt.isPresent()) {
                log.warn("许可证不存在: {}", licenseId);
                return false;
            }
            
            License license = licenseOpt.get();
            
            // 检查许可证状态
            if (!license.isValid()) {
                log.warn("许可证状态无效: {}", licenseId);
                return false;
            }
            
            // 检查是否过期
            if (license.isExpired()) {
                log.warn("许可证已过期: {}", licenseId);
                return false;
            }
            
            // 检查机器绑定
            if (license.getMachineId() != null && !license.getMachineId().equals(machineId)) {
                log.warn("机器ID不匹配，许可证ID: {}, 期望: {}, 实际: {}", 
                        licenseId, license.getMachineId(), machineId);
                return false;
            }
            
            // 更新验证信息
            license.incrementVerifyCount();
            licenseRepository.save(license);
            
            log.info("许可证验证成功: {}", licenseId);
            return true;
            
        } catch (Exception e) {
            log.error("许可证验证失败: " + licenseId, e);
            return false;
        }
    }
    
    /**
     * 获取许可证信息
     * 
     * @param licenseId 许可证ID
     * @return 许可证信息
     */
    public LicenseInfo getLicenseInfo(String licenseId) {
        Optional<License> licenseOpt = licenseRepository.findByLicenseId(licenseId);
        if (!licenseOpt.isPresent()) {
            throw new LicenseException.LicenseNotFoundException("许可证不存在: " + licenseId);
        }
        
        return convertToLicenseInfo(licenseOpt.get());
    }
    
    /**
     * 获取客户的所有许可证
     * 
     * @param customerId 客户ID
     * @return 许可证列表
     */
    public List<LicenseInfo> getCustomerLicenses(String customerId) {
        List<License> licenses = licenseRepository.findByCustomerId(customerId);
        return licenses.stream()
                .map(this::convertToLicenseInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 撤销许可证
     * 
     * @param licenseId 许可证ID
     */
    @Transactional
    public void revokeLicense(String licenseId) {
        Optional<License> licenseOpt = licenseRepository.findByLicenseId(licenseId);
        if (!licenseOpt.isPresent()) {
            throw new LicenseException.LicenseNotFoundException("许可证不存在: " + licenseId);
        }
        
        License license = licenseOpt.get();
        license.setStatus(LicenseConstants.Status.INVALID);
        licenseRepository.save(license);
        
        log.info("许可证已撤销: {}", licenseId);
    }
    
    /**
     * 续期许可证
     * 
     * @param licenseId 许可证ID
     * @param newExpireTime 新的过期时间
     */
    @Transactional
    public void renewLicense(String licenseId, LocalDateTime newExpireTime) {
        Optional<License> licenseOpt = licenseRepository.findByLicenseId(licenseId);
        if (!licenseOpt.isPresent()) {
            throw new LicenseException.LicenseNotFoundException("许可证不存在: " + licenseId);
        }
        
        License license = licenseOpt.get();
        license.setExpireTime(newExpireTime);
        
        // 重新生成签名
        String signature = generateSignature(license);
        license.setSignature(signature);
        
        licenseRepository.save(license);
        
        log.info("许可证续期成功: {}, 新过期时间: {}", licenseId, newExpireTime);
    }

    /**
     * 许可证同步（统一的注册和验证接口）
     *
     * @param machineInfo 机器信息
     * @return 许可证文件响应
     */
    @Transactional
    public LicenseFileResponse syncLicense(MachineInfo machineInfo) {
        try {
            String machineId = machineInfo.getMachineId();

            // 检查机器是否已经注册过
            List<License> existingLicenses = licenseRepository.findByMachineId(machineId);
            for (License license : existingLicenses) {
                if (license.isValid() && !license.isExpired()) {
                    log.info("机器已存在有效许可证，返回最新证书: {}", machineId);
                    // 更新验证时间
                    license.incrementVerifyCount();
                    licenseRepository.save(license);
                    return generateLicenseFileResponse(license);
                }
            }

            // 机器未注册或许可证已过期，创建新的许可证
            LicenseInfo licenseInfo = new LicenseInfo();
            licenseInfo.setCustomerName("Auto-Generated-" + machineId.substring(0, 8));
            licenseInfo.setProductName("Default Product");
            licenseInfo.setLicenseType(LicenseConstants.Type.TRIAL);
            licenseInfo.setMachineId(machineId);

            // 设置试用期30天
            LocalDateTime expireTime = LocalDateTime.now().plusDays(30);
            licenseInfo.setExpireTime(expireTime);

            // 生成许可证
            License license = generateLicense(licenseInfo);

            log.info("为机器创建新许可证: {}, 机器ID: {}", license.getLicenseId(), machineId);
            return generateLicenseFileResponse(license);

        } catch (Exception e) {
            log.error("许可证同步失败: " + machineInfo.getMachineId(), e);
            return null;
        }
    }

    /**
     * 生成许可证ID
     *
     * @return 许可证ID
     */
    private String generateLicenseId() {
        return "LIC-" + UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }
    
    /**
     * 生成激活码
     * 
     * @return 激活码
     */
    private String generateActivationCode() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }
    
    /**
     * 生成数字签名
     * 
     * @param license 许可证
     * @return 数字签名
     */
    private String generateSignature(License license) {
        try {
            // 构建签名数据
            String signData = license.getLicenseId() + ";" + 
                            license.getCustomerId() + ";" + 
                            license.getMachineId() + ";" + 
                            license.getExpireTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            
            // 获取私钥
            PrivateKey privateKey = keyManagementService.getPrivateKey();
            
            // 生成签名
            byte[] signature = CryptoUtil.signRSA(signData.getBytes(), privateKey);
            
            return CryptoUtil.base64Encode(signature);
            
        } catch (Exception e) {
            log.error("生成数字签名失败", e);
            throw new RuntimeException("生成数字签名失败", e);
        }
    }

    /**
     * 生成许可证文件响应
     *
     * @param license 许可证实体
     * @return 许可证文件响应
     */
    private LicenseFileResponse generateLicenseFileResponse(License license) {
        try {
            // 生成许可证文件内容
            byte[] licenseFileContent = generateLicenseFileContent(license);

            // 获取公钥内容
            String publicKeyContent = keyManagementService.getPublicKeyPem();

            return LicenseFileResponse.builder()
                    .licenseFileContent(CryptoUtil.base64Encode(licenseFileContent))
                    .publicKeyContent(publicKeyContent)
                    .licenseId(license.getLicenseId())
                    .machineId(license.getMachineId())
                    .customerName(license.getCustomerName())
                    .productName(license.getProductName())
                    .expireTime(license.getExpireTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())
                    .build();

        } catch (Exception e) {
            log.error("生成许可证文件响应失败: " + license.getLicenseId(), e);
            throw new RuntimeException("生成许可证文件响应失败", e);
        }
    }

    /**
     * 生成许可证文件内容（字节数组）
     *
     * @param license 许可证实体
     * @return 许可证文件内容
     */
    private byte[] generateLicenseFileContent(License license) throws IOException {
        // 构建许可证数据
        String licenseData = buildLicenseData(license);

        // 生成机器密码（使用机器ID）
        String machinePassword = generateMachinePassword(license.getMachineId());

        // 加密许可证数据
        byte[] encryptedData = CryptoUtil.encryptAES(licenseData.getBytes(StandardCharsets.UTF_8),
                machinePassword.getBytes());

        // 生成数字签名
        PrivateKey privateKey = keyManagementService.getPrivateKey();
        byte[] signature = CryptoUtil.signRSA(encryptedData, privateKey);

        // 使用内存流生成文件内容
        java.io.ByteArrayOutputStream byteArrayOutputStream = new java.io.ByteArrayOutputStream();
        try (DataOutputStream outputStream = new DataOutputStream(byteArrayOutputStream)) {
            // 写入MZ头
            outputStream.write(LicenseConstants.Bytes.MZ_HEADER);

            // 写入加密数据长度
            outputStream.writeInt(encryptedData.length);

            // 写入加密数据
            outputStream.write(encryptedData);

            // 写入填充字节
            outputStream.write(LicenseConstants.Bytes.DATA_PADDING);

            // 写入签名长度
            outputStream.writeInt(signature.length);

            // 写入数字签名
            outputStream.write(signature);
        }

        byte[] fileContent = byteArrayOutputStream.toByteArray();
        log.info("许可证文件内容生成成功，大小: {} 字节", fileContent.length);

        return fileContent;
    }

    /**
     * 转换为许可证信息DTO
     *
     * @param license 许可证实体
     * @return 许可证信息DTO
     */
    private LicenseInfo convertToLicenseInfo(License license) {
        return LicenseInfo.builder()
                .licenseId(license.getLicenseId())
                .customerId(license.getCustomerId())
                .customerName(license.getCustomerName())
                .productName(license.getProductName())
                .productVersion(license.getProductVersion())
                .licenseType(license.getLicenseType())
                .machineId(license.getMachineId())
                .status(license.getStatus())
                .issueTime(license.getIssueTime())
                .effectiveTime(license.getEffectiveTime())
                .expireTime(license.getExpireTime())
                .maxUsers(license.getMaxUsers())
                .maxConcurrent(license.getMaxConcurrent())
                .modules(gson.fromJson(license.getModules(), String[].class))
                .signature(license.getSignature())
                .createTime(license.getCreateTime())
                .updateTime(license.getUpdateTime())
                .remarks(license.getRemarks())
                .build();
    }



    /**
     * 构建许可证数据字符串
     *
     * @param license 许可证实体
     * @return 许可证数据字符串
     */
    private String buildLicenseData(License license) {
        // 格式: machineId;expireTime;status;customerName;productName;licenseType
        StringBuilder builder = new StringBuilder();
        builder.append(license.getMachineId()).append(";");
        builder.append(license.getExpireTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()).append(";");
        builder.append(license.getStatus()).append(";");
        builder.append(license.getCustomerName()).append(";");
        builder.append(license.getProductName()).append(";");
        builder.append(license.getLicenseType());

        return builder.toString();
    }

    /**
     * 生成机器密码
     *
     * @param machineId 机器ID
     * @return 机器密码
     */
    private String generateMachinePassword(String machineId) {
        if (machineId == null || machineId.length() < 16) {
            return LicenseConstants.Crypto.DEFAULT_KEY;
        }

        // 使用机器ID的前16位作为密码
        String password = machineId.replaceAll("[\\-\\s]", "");
        if (password.length() > 16) {
            password = password.substring(0, 16);
        } else if (password.length() < 16) {
            password = String.format("%-16s", password).replace(' ', '0');
        }

        return password;
    }
}
