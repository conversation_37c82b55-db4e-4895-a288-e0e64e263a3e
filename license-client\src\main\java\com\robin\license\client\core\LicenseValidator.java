package com.robin.license.client.core;

import com.robin.license.client.hardware.MachineInfoCollector;
import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.crypto.CryptoUtil;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.exception.LicenseException;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 许可证验证器
 * 负责本地许可证文件的验证逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class LicenseValidator {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 验证许可证
     * 
     * @return 许可证信息
     * @throws LicenseException 验证失败时抛出异常
     */
    public static LicenseInfo validateLicense() throws LicenseException {
        // 获取许可证文件路径
        File licenseFile = getLicenseFile();
        if (!licenseFile.exists()) {
            throw new LicenseException.LicenseNotFoundException("许可证文件不存在: " + licenseFile.getAbsolutePath());
        }
        
        // 获取公钥
        PublicKey publicKey = getPublicKey();
        if (publicKey == null) {
            throw new LicenseException.LicenseInvalidException("无法获取公钥");
        }
        
        // 获取当前机器信息
        MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();
        
        // 读取并验证许可证文件
        return validateLicenseFile(licenseFile, publicKey, machineInfo);
    }
    
    /**
     * 验证许可证文件
     * 
     * @param licenseFile 许可证文件
     * @param publicKey 公钥
     * @param machineInfo 机器信息
     * @return 许可证信息
     * @throws LicenseException 验证失败时抛出异常
     */
    private static LicenseInfo validateLicenseFile(File licenseFile, PublicKey publicKey, MachineInfo machineInfo) 
            throws LicenseException {
        
        try (DataInputStream inputStream = new DataInputStream(new FileInputStream(licenseFile))) {
            // 读取并验证文件头
            byte[] header = new byte[LicenseConstants.Bytes.MZ_HEADER.length];
            inputStream.read(header);
            if (!java.util.Arrays.equals(header, LicenseConstants.Bytes.MZ_HEADER)) {
                throw new LicenseException.LicenseInvalidException("许可证文件头无效");
            }
            
            // 读取加密数据长度
            int encryptedDataLength = inputStream.readInt();
            if (encryptedDataLength <= 0) {
                throw new LicenseException.LicenseInvalidException("许可证数据长度无效");
            }
            
            // 读取加密数据
            byte[] encryptedData = new byte[encryptedDataLength];
            inputStream.read(encryptedData);
            
            // 检查数据有效性
            if (encryptedData[encryptedData.length - 1] == (byte) 0) {
                throw new LicenseException.LicenseInvalidException("许可证数据无效");
            }
            
            // 解密许可证数据
            String machinePassword = generateMachinePassword(machineInfo);
            byte[] decryptedData = CryptoUtil.decryptAES(encryptedData, machinePassword.getBytes());
            String licenseData = new String(decryptedData, "UTF-8");
            
            // 解析许可证数据
            LicenseInfo licenseInfo = parseLicenseData(licenseData);
            
            // 验证机器绑定
            if (!machineInfo.verifyFingerprint(licenseInfo.getMachineId())) {
                throw new LicenseException.MachineMismatchException("许可证与当前机器不匹配");
            }
            
            // 验证有效期
            if (licenseInfo.isExpired()) {
                throw new LicenseException.LicenseExpiredException("许可证已过期，过期时间: " + 
                        licenseInfo.getExpireTime().format(DATE_FORMATTER));
            }
            
            // 读取填充字节
            byte[] padding = new byte[1];
            inputStream.read(padding);
            if (!java.util.Arrays.equals(padding, LicenseConstants.Bytes.DATA_PADDING)) {
                throw new LicenseException.LicenseInvalidException("许可证格式错误");
            }
            
            // 读取签名长度
            int signatureLength = inputStream.readInt();
            if (signatureLength <= 0) {
                throw new LicenseException.LicenseInvalidException("签名长度无效");
            }
            
            // 读取数字签名
            byte[] signature = new byte[signatureLength];
            inputStream.read(signature);
            
            // 验证数字签名
            if (!CryptoUtil.verifyRSA(encryptedData, signature, publicKey)) {
                throw new LicenseException.SignatureVerifyException("许可证数字签名验证失败");
            }
            
            log.info("许可证验证成功，客户: {}, 产品: {}, 过期时间: {}", 
                    licenseInfo.getCustomerName(), 
                    licenseInfo.getProductName(),
                    licenseInfo.getExpireTime().format(DATE_FORMATTER));
            
            return licenseInfo;
            
        } catch (LicenseException e) {
            throw e;
        } catch (Exception e) {
            log.error("许可证验证失败", e);
            throw new LicenseException.LicenseInvalidException("许可证验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析许可证数据
     * 
     * @param licenseData 许可证数据字符串
     * @return 许可证信息
     */
    private static LicenseInfo parseLicenseData(String licenseData) {
        // 许可证数据格式: machineId;expireTime;status;customerName;productName;licenseType;...
        String[] parts = licenseData.split(";");
        if (parts.length < 6) {
            throw new LicenseException.LicenseInvalidException("许可证数据格式错误");
        }
        
        return LicenseInfo.builder()
                .machineId(parts[0])
                .expireTime(LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(Long.parseLong(parts[1])), 
                        ZoneId.systemDefault()))
                .status(parts[2])
                .customerName(parts[3])
                .productName(parts[4])
                .licenseType(parts[5])
                .build();
    }
    
    /**
     * 生成机器密码
     * 
     * @param machineInfo 机器信息
     * @return 机器密码
     */
    private static String generateMachinePassword(MachineInfo machineInfo) {
        StringBuilder builder = new StringBuilder();
        
        if (machineInfo.getMachineId() != null) {
            builder.append(machineInfo.getMachineId());
        }
        if (machineInfo.getCpuSerial() != null) {
            builder.append(machineInfo.getCpuSerial());
        }
        if (machineInfo.getMotherboardSerial() != null) {
            builder.append(machineInfo.getMotherboardSerial());
        }
        
        String password = builder.toString().replaceAll("[\\-\\s]", "");
        
        // 如果密码太短，使用默认密钥
        if (password.length() < 16) {
            password = LicenseConstants.Crypto.DEFAULT_KEY;
        }
        
        // 截取或填充到16位
        if (password.length() > 16) {
            password = password.substring(0, 16);
        } else if (password.length() < 16) {
            password = String.format("%-16s", password).replace(' ', '0');
        }
        
        return password;
    }
    
    /**
     * 获取许可证文件
     * 
     * @return 许可证文件
     */
    private static File getLicenseFile() {
        String userHome = System.getProperty(LicenseConstants.System.USER_HOME);
        File licenseDir = new File(userHome, LicenseConstants.File.LICENSE_DIR);
        return new File(licenseDir, LicenseConstants.File.LICENSE_FILE);
    }
    
    /**
     * 获取公钥
     * 
     * @return 公钥对象
     */
    private static PublicKey getPublicKey() {
        try {
            // 首先尝试从用户目录读取
            String userHome = System.getProperty(LicenseConstants.System.USER_HOME);
            File licenseDir = new File(userHome, LicenseConstants.File.LICENSE_DIR);
            File publicKeyFile = new File(licenseDir, LicenseConstants.File.PUBLIC_KEY_FILE);
            
            if (publicKeyFile.exists()) {
                try (FileReader reader = new FileReader(publicKeyFile)) {
                    StringBuilder keyContent = new StringBuilder();
                    char[] buffer = new char[1024];
                    int length;
                    while ((length = reader.read(buffer)) != -1) {
                        keyContent.append(buffer, 0, length);
                    }
                    return CryptoUtil.readPublicKeyFromPem(keyContent.toString());
                }
            }
            
            // 如果用户目录没有，尝试从classpath读取
            try (InputStream is = LicenseValidator.class.getClassLoader()
                    .getResourceAsStream(LicenseConstants.File.PUBLIC_KEY_FILE)) {
                if (is != null) {
                    StringBuilder keyContent = new StringBuilder();
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            keyContent.append(line).append("\n");
                        }
                    }
                    return CryptoUtil.readPublicKeyFromPem(keyContent.toString());
                }
            }
            
        } catch (Exception e) {
            log.error("读取公钥失败", e);
        }
        
        return null;
    }
}
