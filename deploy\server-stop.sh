#!/bin/bash

# License Server Stop Script
# 许可证服务器停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
APP_NAME="license-server"
PID_FILE="$APP_NAME.pid"
SHUTDOWN_TIMEOUT=30

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  License Server 停止脚本${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            return 0
        else
            rm -f $PID_FILE
            return 1
        fi
    fi
    return 1
}

# 优雅停止服务
graceful_stop() {
    if ! is_running; then
        echo -e "${YELLOW}License Server 未运行${NC}"
        return 0
    fi
    
    PID=$(cat $PID_FILE)
    echo -e "${YELLOW}正在停止 License Server (PID: $PID)...${NC}"
    
    # 发送TERM信号
    kill -TERM $PID
    
    # 等待进程结束
    echo -e "${YELLOW}等待进程优雅退出...${NC}"
    for i in $(seq 1 $SHUTDOWN_TIMEOUT); do
        if ! is_running; then
            echo -e "${GREEN}License Server 已成功停止${NC}"
            rm -f $PID_FILE
            return 0
        fi
        sleep 1
        echo -n "."
    done
    echo
    
    # 如果优雅停止失败，强制停止
    echo -e "${YELLOW}优雅停止超时，强制停止进程...${NC}"
    force_stop
}

# 强制停止服务
force_stop() {
    if ! is_running; then
        echo -e "${YELLOW}License Server 未运行${NC}"
        return 0
    fi
    
    PID=$(cat $PID_FILE)
    echo -e "${YELLOW}强制停止 License Server (PID: $PID)...${NC}"
    
    # 发送KILL信号
    kill -KILL $PID
    
    # 等待进程结束
    sleep 2
    
    if ! is_running; then
        echo -e "${GREEN}License Server 已强制停止${NC}"
        rm -f $PID_FILE
    else
        echo -e "${RED}无法停止 License Server${NC}"
        exit 1
    fi
}

# 停止所有相关进程
stop_all() {
    echo -e "${YELLOW}查找所有 License Server 进程...${NC}"
    
    # 查找Java进程中包含license-server的进程
    PIDS=$(ps aux | grep java | grep license-server | grep -v grep | awk '{print $2}' || true)
    
    if [ -z "$PIDS" ]; then
        echo -e "${YELLOW}未找到运行中的 License Server 进程${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}找到以下进程:${NC}"
    ps aux | grep java | grep license-server | grep -v grep
    
    for PID in $PIDS; do
        echo -e "${YELLOW}停止进程 $PID...${NC}"
        kill -TERM $PID
    done
    
    # 等待进程结束
    sleep 5
    
    # 检查是否还有残留进程
    REMAINING_PIDS=$(ps aux | grep java | grep license-server | grep -v grep | awk '{print $2}' || true)
    
    if [ -n "$REMAINING_PIDS" ]; then
        echo -e "${YELLOW}强制停止残留进程...${NC}"
        for PID in $REMAINING_PIDS; do
            kill -KILL $PID
        done
    fi
    
    echo -e "${GREEN}所有 License Server 进程已停止${NC}"
    rm -f $PID_FILE
}

# 显示状态
show_status() {
    if is_running; then
        PID=$(cat $PID_FILE)
        echo -e "${GREEN}License Server 正在运行 (PID: $PID)${NC}"
    else
        echo -e "${RED}License Server 未运行${NC}"
    fi
}

# 清理资源
cleanup() {
    echo -e "${YELLOW}清理临时文件...${NC}"
    
    # 清理PID文件
    if [ -f "$PID_FILE" ]; then
        rm -f $PID_FILE
        echo -e "${GREEN}已清理PID文件${NC}"
    fi
    
    # 清理临时日志文件
    if [ -d "logs" ]; then
        find logs -name "*.tmp" -delete 2>/dev/null || true
        echo -e "${GREEN}已清理临时日志文件${NC}"
    fi
}

# 主函数
main() {
    case "${1:-stop}" in
        stop)
            graceful_stop
            ;;
        force-stop)
            force_stop
            ;;
        stop-all)
            stop_all
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            echo "用法: $0 [命令]"
            echo "命令:"
            echo "  stop       优雅停止服务 (默认)"
            echo "  force-stop 强制停止服务"
            echo "  stop-all   停止所有相关进程"
            echo "  status     显示状态"
            echo "  cleanup    清理临时文件"
            echo "  help       显示帮助"
            ;;
        *)
            echo -e "${RED}未知命令: $1${NC}"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
