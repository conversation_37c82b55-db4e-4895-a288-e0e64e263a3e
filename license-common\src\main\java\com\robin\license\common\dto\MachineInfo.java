package com.robin.license.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 机器信息数据传输对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MachineInfo {
    
    /**
     * 机器唯一标识
     */
    private String machineId;
    
    /**
     * CPU序列号
     */
    private String cpuSerial;
    
    /**
     * 主板序列号
     */
    private String motherboardSerial;
    
    /**
     * 硬盘序列号
     */
    private String diskSerial;
    
    /**
     * 操作系统名称
     */
    private String osName;
    
    /**
     * 操作系统版本
     */
    private String osVersion;
    
    /**
     * 操作系统架构
     */
    private String osArch;
    
    /**
     * 主机名
     */
    private String hostname;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * MAC地址
     */
    private String macAddress;
    
    /**
     * 内存大小（MB）
     */
    private Long memorySize;
    
    /**
     * 处理器核心数
     */
    private Integer processorCount;
    
    /**
     * Java版本
     */
    private String javaVersion;
    
    /**
     * Java供应商
     */
    private String javaVendor;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 用户目录
     */
    private String userHome;
    
    /**
     * 工作目录
     */
    private String workingDir;
    
    /**
     * 时区
     */
    private String timezone;
    
    /**
     * 机器指纹（综合标识）
     */
    private String fingerprint;
    
    /**
     * 信息收集时间
     */
    private LocalDateTime collectTime;
    
    /**
     * 生成机器指纹
     * 基于多个硬件标识生成唯一指纹
     * 
     * @return 机器指纹
     */
    public String generateFingerprint() {
        StringBuilder builder = new StringBuilder();
        
        if (machineId != null) {
            builder.append(machineId);
        }
        if (cpuSerial != null) {
            builder.append(cpuSerial);
        }
        if (motherboardSerial != null) {
            builder.append(motherboardSerial);
        }
        if (diskSerial != null) {
            builder.append(diskSerial);
        }
        
        // 移除特殊字符
        String raw = builder.toString().replaceAll("[\\-\\s]", "");
        
        // 生成MD5哈希作为指纹
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(raw.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            this.fingerprint = hexString.toString().toUpperCase();
            return this.fingerprint;
        } catch (Exception e) {
            // 如果哈希失败，直接返回原始字符串
            this.fingerprint = raw;
            return this.fingerprint;
        }
    }
    
    /**
     * 验证机器指纹是否匹配
     * 
     * @param targetFingerprint 目标指纹
     * @return 是否匹配
     */
    public boolean verifyFingerprint(String targetFingerprint) {
        if (targetFingerprint == null || targetFingerprint.trim().isEmpty()) {
            return false;
        }
        
        String currentFingerprint = this.fingerprint;
        if (currentFingerprint == null) {
            currentFingerprint = generateFingerprint();
        }
        
        return targetFingerprint.equalsIgnoreCase(currentFingerprint);
    }
}
